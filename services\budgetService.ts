import { auth, db, fallbackServices } from '@/firebaseConfig';
import { collection, doc, getDoc, getDocs, setDoc, writeBatch } from 'firebase/firestore';

// Type pour les budgets par catégorie
export interface CategoryBudget {
  category: string;
  amount: number;
  spent?: number;
}

// Fonction pour récupérer les budgets par catégorie
export const getCategoryBudgets = async (): Promise<CategoryBudget[]> => {
  try {
    // Vérifier si Firebase est disponible
    if (!auth || !db) {
      console.log("Firebase non disponible, utilisation du stockage local");
      return fallbackServices.getCategoryBudgets();
    }
    
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log("Utilisateur non connecté, utilisation du stockage local");
      return fallbackServices.getCategoryBudgets();
    }
    
    const budgetsRef = collection(db, 'users', currentUser.uid, 'budgets');
    const budgetsSnapshot = await getDocs(budgetsRef);
    
    const budgets: CategoryBudget[] = [];
    
    budgetsSnapshot.forEach(doc => {
      if (doc.id !== 'global') {
        const data = doc.data();
        budgets.push({
          category: doc.id,
          amount: data.amount || 0,
          spent: data.spent || 0
        });
      }
    });
    
    return budgets;
  } catch (error) {
    console.error('Erreur lors de la récupération des budgets par catégorie:', error);
    return fallbackServices.getCategoryBudgets();
  }
};

// Fonction pour récupérer le budget global
export const getGlobalBudget = async (): Promise<number> => {
  try {
    // Vérifier si Firebase est disponible
    if (!auth || !db) {
      console.log("Firebase non disponible, utilisation du stockage local");
      return fallbackServices.getGlobalBudget();
    }
    
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log("Utilisateur non connecté, utilisation du stockage local");
      return fallbackServices.getGlobalBudget();
    }
    
    const globalBudgetRef = doc(db, 'users', currentUser.uid, 'budgets', 'global');
    const globalBudgetDoc = await getDoc(globalBudgetRef);
    
    if (globalBudgetDoc.exists()) {
      const data = globalBudgetDoc.data();
      return data.amount || 0;
    }
    
    return 0;
  } catch (error) {
    console.error('Erreur lors de la récupération du budget global:', error);
    return fallbackServices.getGlobalBudget();
  }
};

// Fonction pour calculer le total des dépenses
export const calculateTotalSpent = async (): Promise<number> => {
  try {
    // Vérifier si Firebase est disponible
    if (!auth || !db) {
      console.log("Firebase non disponible, utilisation du stockage local");
      return fallbackServices.calculateTotalSpent();
    }
    
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log("Utilisateur non connecté, utilisation du stockage local");
      return fallbackServices.calculateTotalSpent();
    }
    
    const budgetsRef = collection(db, 'users', currentUser.uid, 'budgets');
    const budgetsSnapshot = await getDocs(budgetsRef);
    
    let totalSpent = 0;
    
    budgetsSnapshot.forEach(doc => {
      if (doc.id !== 'global') {
        const data = doc.data();
        totalSpent += data.spent || 0;
      }
    });
    
    return totalSpent;
  } catch (error) {
    console.error('Erreur lors du calcul des dépenses totales:', error);
    return fallbackServices.calculateTotalSpent();
  }
};

// Fonction pour mettre à jour le budget d'une catégorie
export const updateCategoryBudget = async (category: string, amount: number): Promise<{ success: boolean, error?: any }> => {
  try {
    // Vérifier si Firebase est disponible
    if (!auth || !db) {
      console.log("Firebase non disponible, utilisation du stockage local");
      return fallbackServices.updateCategoryBudget(category, amount);
    }
    
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log("Utilisateur non connecté, utilisation du stockage local");
      return fallbackServices.updateCategoryBudget(category, amount);
    }
    
    const budgetRef = doc(db, 'users', currentUser.uid, 'budgets', category);
    const budgetDoc = await getDoc(budgetRef);
    
    if (budgetDoc.exists()) {
      const data = budgetDoc.data();
      await setDoc(budgetRef, {
        ...data,
        amount: amount,
        updatedAt: new Date()
      }, { merge: true });
    } else {
      await setDoc(budgetRef, {
        amount: amount,
        spent: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
    
    return { success: true };
  } catch (error) {
    console.error('Erreur lors de la mise à jour du budget de catégorie:', error);
    return { success: false, error };
  }
};

// Fonction pour transférer du budget entre catégories
export const transferBudgetBetweenCategories = async (
  fromCategory: string, 
  toCategory: string, 
  amount: number
): Promise<{ success: boolean, error?: any }> => {
  try {
    // Vérifier si Firebase est disponible
    if (!auth || !db) {
      console.log("Firebase non disponible, utilisation du stockage local");
      return fallbackServices.transferBudgetBetweenCategories(fromCategory, toCategory, amount);
    }
    
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log("Utilisateur non connecté, utilisation du stockage local");
      return fallbackServices.transferBudgetBetweenCategories(fromCategory, toCategory, amount);
    }
    
    // Récupérer les budgets actuels
    const fromBudgetRef = doc(db, 'users', currentUser.uid, 'budgets', fromCategory);
    const toBudgetRef = doc(db, 'users', currentUser.uid, 'budgets', toCategory);
    
    const fromBudgetDoc = await getDoc(fromBudgetRef);
    if (!fromBudgetDoc.exists()) {
      return { success: false, error: new Error(`La catégorie source ${fromCategory} n'existe pas`) };
    }
    
    const fromBudgetData = fromBudgetDoc.data();
    if (fromBudgetData.amount < amount) {
      return { success: false, error: new Error("Budget insuffisant pour le transfert") };
    }
    
    // Mettre à jour le budget source
    await setDoc(fromBudgetRef, {
      ...fromBudgetData,
      amount: fromBudgetData.amount - amount,
      updatedAt: new Date()
    }, { merge: true });
    
    // Mettre à jour le budget destination
    const toBudgetDoc = await getDoc(toBudgetRef);
    const toBudgetData = toBudgetDoc.exists() ? toBudgetDoc.data() : { amount: 0 };
    
    await setDoc(toBudgetRef, {
      ...toBudgetData,
      amount: (toBudgetData.amount || 0) + amount,
      updatedAt: new Date()
    }, { merge: true });
    
    return { success: true };
  } catch (error) {
    console.error('Erreur lors du transfert de budget:', error);
    return { success: false, error };
  }
};

// Fonction pour calculer le budget total à partir des budgets par catégorie
export const calculateTotalBudget = async (): Promise<number> => {
  try {
    // Vérifier si Firebase est disponible
    if (!auth || !db) {
      console.log("Firebase non disponible, utilisation du stockage local");
      return fallbackServices.calculateTotalBudget();
    }
    
    const budgets = await getCategoryBudgets();
    return budgets.reduce((total, budget) => total + budget.amount, 0);
  } catch (error) {
    console.error('Erreur lors du calcul du budget total:', error);
    return fallbackServices.calculateTotalBudget();
  }
};

// Fonction pour réinitialiser tous les budgets
export const resetAllBudgets = async (): Promise<boolean> => {
  try {
    // Vérifier si Firebase est disponible
    if (!auth || !db) {
      console.log("Firebase non disponible, utilisation du stockage local");
      const result = await fallbackServices.resetAllBudgets();
      return result.success;
    }

    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log("Utilisateur non connecté, utilisation du stockage local");
      const result = await fallbackServices.resetAllBudgets();
      return result.success;
    }

    const budgetsRef = collection(db, 'users', currentUser.uid, 'budgets');
    await setDoc(doc(budgetsRef, 'global'), { amount: 0 });

    const budgetsSnapshot = await getDocs(budgetsRef);
    const batch = writeBatch(db);

    budgetsSnapshot.forEach(doc => {
      batch.update(doc.ref, { amount: 0, spent: 0 });
    });

    await batch.commit();

    return true;
  } catch (error) {
    console.error('Erreur lors de la réinitialisation des budgets:', error);
    const result = await fallbackServices.resetAllBudgets();
    return result.success;
  }
};

// Fonction pour sauvegarder le budget global
export const saveGlobalBudget = async (amount: string): Promise<boolean> => {
  try {
    // Vérifier si Firebase est disponible
    if (!auth || !db) {
      console.log("Firebase non disponible, utilisation du stockage local");
      return fallbackServices.saveGlobalBudget(amount);
    }
    
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log("Utilisateur non connecté, utilisation du stockage local");
      return fallbackServices.saveGlobalBudget(amount);
    }
    
    const budgetsRef = collection(db, 'users', currentUser.uid, 'budgets');
    await setDoc(doc(budgetsRef, 'global'), { amount: parseFloat(amount) });
    
    return true;
  } catch (error) {
    console.error('Erreur lors de la sauvegarde du budget global:', error);
    return fallbackServices.saveGlobalBudget(amount);
  }
};

// Fonction pour récupérer le budget global sous forme de chaîne
export const getGlobalBudgetAsString = async (): Promise<string> => {
  try {
    // Vérifier si Firebase est disponible
    if (!auth || !db) {
      console.log("Firebase non disponible, utilisation du stockage local");
      const budget = await fallbackServices.getGlobalBudget();
      return budget.toString();
    }
    
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log("Utilisateur non connecté, utilisation du stockage local");
      const budget = await fallbackServices.getGlobalBudget();
      return budget.toString();
    }
    
    const budgetsRef = collection(db, 'users', currentUser.uid, 'budgets');
    const globalBudgetDoc = doc(budgetsRef, 'global');
    
    const globalBudgetSnapshot = await getDoc(globalBudgetDoc);
    if (globalBudgetSnapshot.exists()) {
      return globalBudgetSnapshot.data()!.amount!.toString();
    }
    
    return '0';
  } catch (error) {
    console.error('Erreur lors de la récupération du budget global:', error);
    const budget = await fallbackServices.getGlobalBudget();
    return budget.toString();
  }
};

// Fonction pour sauvegarder les budgets par catégorie
export const saveCategoryBudgets = async (budgets: CategoryBudget[]): Promise<boolean> => {
  try {
    // Vérifier si Firebase est disponible
    if (!auth || !db) {
      console.log("Firebase non disponible, utilisation du stockage local");
      return fallbackServices.saveCategoryBudgets(budgets);
    }
    
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log("Utilisateur non connecté, utilisation du stockage local");
      return fallbackServices.saveCategoryBudgets(budgets);
    }
    
    const budgetsRef = collection(db, 'users', currentUser.uid, 'budgets');
    
    const batch = writeBatch(db);
    
    budgets.forEach(budget => {
      const categoryDoc = doc(budgetsRef, budget.category);
      batch.set(categoryDoc, { amount: budget.amount, spent: 0 });
    });
    
    await batch.commit();
    
    return true;
  } catch (error) {
    console.error('Erreur lors de la sauvegarde des budgets par catégorie:', error);
    return fallbackServices.saveCategoryBudgets(budgets);
  }
};

// Fonction pour récupérer le budget pour une catégorie spécifique
export const getBudgetForCategory = async (category: string): Promise<CategoryBudget> => {
  try {
    // Vérifier si Firebase est disponible
    if (!auth || !db) {
      console.log("Firebase non disponible, utilisation du stockage local");
      return fallbackServices.getBudgetForCategory(category);
    }
    
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log("Utilisateur non connecté, utilisation du stockage local");
      return fallbackServices.getBudgetForCategory(category);
    }
    
    const budgetRef = doc(db, 'users', currentUser.uid, 'budgets', category);
    const budgetDoc = await getDoc(budgetRef);
    
    if (budgetDoc.exists()) {
      const data = budgetDoc.data();
      return {
        category,
        amount: data.amount || 0,
        spent: data.spent || 0
      };
    }
    
    return {
      category,
      amount: 0,
      spent: 0
    };
  } catch (error) {
    console.error(`Erreur lors de la récupération du budget pour la catégorie ${category}:`, error);
    return fallbackServices.getBudgetForCategory(category);
  }
};


