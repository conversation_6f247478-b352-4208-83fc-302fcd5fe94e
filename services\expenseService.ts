import { auth, db, fallbackServices } from '@/firebaseConfig.js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { collection, deleteDoc, doc, getDoc, getDocs, setDoc } from 'firebase/firestore';

// Type pour les dépenses
export interface Expense {
  id: string;
  amount: number;
  category: string;
  date: string;
  createdAt: string;
}

// Fonction pour récupérer les dépenses de l'utilisateur
export const getUserExpenses = async (): Promise<Expense[]> => {
  try {
    // Vérifier si Firebase est disponible
    if (!auth || !db) {
      console.log("Firebase non disponible, utilisation du stockage local");
      return fallbackServices.getUserExpenses();
    }
    
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log("Utilisateur non connecté, utilisation du stockage local");
      return fallbackServices.getUserExpenses();
    }
    
    const expensesRef = collection(db, 'users', currentUser.uid, 'expenses');
    const expensesSnapshot = await getDocs(expensesRef);
    
    const expenses: Expense[] = [];
    expensesSnapshot.forEach(doc => {
      const data = doc.data();
      expenses.push({
        id: doc.id,
        amount: data.amount,
        category: data.category,
        date: data.date,
        createdAt: data.createdAt
      });
    });
    
    // Trier par date (plus récent en premier)
    return expenses.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  } catch (error) {
    console.error('Erreur lors de la récupération des dépenses:', error);
    return fallbackServices.getUserExpenses();
  }
};

// Fonction pour ajouter une dépense
export const addExpense = async (amount: number, category: string, date: string): Promise<{ success: boolean, expense?: Expense, error?: any }> => {
  try {
    // Vérifier si Firebase est disponible
    if (!auth || !db) {
      console.log("Firebase non disponible, utilisation du stockage local");
      return fallbackServices.addExpense(amount, category, date);
    }
    
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log("Utilisateur non connecté, utilisation du stockage local");
      return fallbackServices.addExpense(amount, category, date);
    }
    
    // Créer un ID unique pour la dépense
    const expenseId = doc(collection(db, 'users', currentUser.uid, 'expenses')).id;
    const expenseRef = doc(db, 'users', currentUser.uid, 'expenses', expenseId);
    
    const now = new Date().toISOString();
    const expenseData = {
      amount: amount,
      category: category,
      date: date,
      createdAt: now,
      userId: currentUser.uid
    };
    
    await setDoc(expenseRef, expenseData);
    
    // Mettre à jour le montant dépensé dans la catégorie de budget
    const budgetRef = doc(db, 'users', currentUser.uid, 'budgets', category);
    const budgetDoc = await getDoc(budgetRef);
    
    if (budgetDoc.exists()) {
      const budgetData = budgetDoc.data();
      await setDoc(budgetRef, {
        ...budgetData,
        spent: (budgetData.spent || 0) + amount,
        updatedAt: now
      }, { merge: true });
    }
    
    return { 
      success: true, 
      expense: {
        id: expenseId,
        amount,
        category,
        date,
        createdAt: now
      }
    };
  } catch (error) {
    console.error('Erreur lors de l\'ajout de la dépense:', error);
    return { success: false, error };
  }
};

// Fonction pour supprimer une dépense
export const deleteExpense = async (expenseId: string): Promise<{ success: boolean, error?: any }> => {
  try {
    // Vérifier si Firebase est disponible
    if (!auth || !db) {
      console.log("Firebase non disponible, utilisation du stockage local");
      return fallbackServices.deleteExpense(expenseId);
    }
    
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log("Utilisateur non connecté, utilisation du stockage local");
      return fallbackServices.deleteExpense(expenseId);
    }
    
    // Récupérer d'abord les informations de la dépense pour mettre à jour le budget
    const expenseRef = doc(db, 'users', currentUser.uid, 'expenses', expenseId);
    const expenseDoc = await getDoc(expenseRef);
    
    if (expenseDoc.exists()) {
      const expenseData = expenseDoc.data();
      
      // Mettre à jour le montant dépensé dans la catégorie de budget
      const budgetRef = doc(db, 'users', currentUser.uid, 'budgets', expenseData.category);
      const budgetDoc = await getDoc(budgetRef);
      
      if (budgetDoc.exists()) {
        const budgetData = budgetDoc.data();
        await setDoc(budgetRef, {
          ...budgetData,
          spent: Math.max(0, (budgetData.spent || 0) - expenseData.amount),
          updatedAt: new Date().toISOString()
        }, { merge: true });
      }
    }
    
    // Supprimer la dépense
    await deleteDoc(expenseRef);
    
    return { success: true };
  } catch (error) {
    console.error('Erreur lors de la suppression de la dépense:', error);
    return { success: false, error };
  }
};

// Fonction pour filtrer les dépenses par date
export const filterExpenses = async (filterParams: ExpenseFilterParams): Promise<Expense[]> => {
  try {
    // Récupérer toutes les dépenses d'abord
    const allExpenses = await getUserExpenses();
    let filteredExpenses = [...allExpenses];
    
    // Appliquer les filtres
    if (filterParams.startDate) {
      filteredExpenses = filteredExpenses.filter(expense => {
        return expense.date >= filterParams.startDate!;
      });
    }
    
    if (filterParams.endDate) {
      filteredExpenses = filteredExpenses.filter(expense => {
        return expense.date <= filterParams.endDate!;
      });
    }
    
    if (filterParams.minAmount !== undefined) {
      filteredExpenses = filteredExpenses.filter(expense => 
        expense.amount >= filterParams.minAmount!
      );
    }
    
    if (filterParams.maxAmount !== undefined) {
      filteredExpenses = filteredExpenses.filter(expense => 
        expense.amount <= filterParams.maxAmount!
      );
    }
    
    if (filterParams.categories && filterParams.categories.length > 0) {
      filteredExpenses = filteredExpenses.filter(expense => 
        filterParams.categories!.includes(expense.category)
      );
    }
    
    // Trier par date (plus récent en premier)
    return filteredExpenses.sort((a, b) => 
      new Date(b.date).getTime() - new Date(a.date).getTime()
    );
  } catch (error) {
    console.error('Erreur lors du filtrage des dépenses:', error);
    throw error;
  }
};

// Fonction pour réinitialiser toutes les dépenses
export const resetAllExpenses = async (): Promise<{ success: boolean, error?: any }> => {
  try {
    // Vérifier si Firebase est disponible
    if (!auth || !db) {
      console.log("Firebase non disponible, utilisation du stockage local");
      return fallbackServices.resetAllExpenses();
    }
    
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log("Utilisateur non connecté, utilisation du stockage local");
      return fallbackServices.resetAllExpenses();
    }
    
    const expensesRef = collection(db, 'users', currentUser.uid, 'expenses');
    const expensesSnapshot = await getDocs(expensesRef);
    
    // Supprimer chaque document de dépense
    const deletePromises = [];
    expensesSnapshot.forEach(document => {
      const docRef = doc(db, 'users', currentUser.uid, 'expenses', document.id);
      deletePromises.push(deleteDoc(docRef));
    });
    
    await Promise.all(deletePromises);
    
    // Réinitialiser également les montants dépensés dans les budgets
    const budgetsRef = collection(db, 'users', currentUser.uid, 'budgets');
    const budgetsSnapshot = await getDocs(budgetsRef);
    
    const resetBudgetPromises = [];
    budgetsSnapshot.forEach(document => {
      const docRef = doc(db, 'users', currentUser.uid, 'budgets', document.id);
      const data = document.data();
      resetBudgetPromises.push(setDoc(docRef, {
        ...data,
        spent: 0,
        updatedAt: new Date().toISOString()
      }, { merge: true }));
    });
    
    await Promise.all(resetBudgetPromises);
    
    return { success: true };
  } catch (error) {
    console.error('Erreur lors de la réinitialisation des dépenses:', error);
    return { success: false, error };
  }
};

// Récupérer les dépenses par date
export const getExpensesByDate = async (date: string): Promise<Expense[]> => {
  try {
    // Vérifier si Firebase est disponible
    if (!auth || !db) {
      console.log("Firebase non disponible, utilisation du stockage local");
      // Implémentation locale
      const expensesJson = await AsyncStorage.getItem('expenses');
      const allExpenses = expensesJson ? JSON.parse(expensesJson) : [];
      return allExpenses.filter((expense: Expense) => expense.date === date);
    }
    
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log("Utilisateur non connecté, utilisation du stockage local");
      const expensesJson = await AsyncStorage.getItem('expenses');
      const allExpenses = expensesJson ? JSON.parse(expensesJson) : [];
      return allExpenses.filter((expense: Expense) => expense.date === date);
    }
    
    // Récupérer toutes les dépenses
    const allExpenses = await getUserExpenses();
    
    // Filtrer par date
    return allExpenses.filter(expense => expense.date === date);
  } catch (error) {
    console.error('Erreur lors de la récupération des dépenses par date:', error);
    return [];
  }
};

// Assurez-vous que cette interface est définie une seule fois
export interface ExpenseFilterParams {
  startDate?: string;
  endDate?: string;
  minAmount?: number;
  maxAmount?: number;
  categories?: string[];
}







