// firebase.js - Fichier principal pour Firebase
import AsyncStorage from '@react-native-async-storage/async-storage';
import { initializeApp } from 'firebase/app';
import { getReactNativePersistence, initializeAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

// Configuration Firebase
const firebaseConfig = {
  apiKey: "AIzaSyDZYVxe-GZ1xWLl2UYVu_hmHCjZM9lNxTs",
  authDomain: "mboabudget.firebaseapp.com",
  projectId: "mboabudget",
  storageBucket: "mboabudget.appspot.com",
  messagingSenderId: "1045282978405",
  appId: "1:1045282978405:web:c9e9f1a7a4d8f3a5a5a5a5"
};

// Initialiser Firebase
let app, auth, db;

try {
  // Initialiser l'application Firebase
  app = initializeApp(firebaseConfig);
  
  // Initialiser Auth avec AsyncStorage persistence
  auth = initializeAuth(app, {
    persistence: getReactNativePersistence(AsyncStorage)
  });
  
  // Initialiser Firestore
  db = getFirestore(app);
  
  console.log("Firebase initialized successfully in firebase.js");
} catch (error) {
  console.error("Firebase initialization error in firebase.js:", error);
}

// Fonctions d'authentification
const loginUser = async (email, password) => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return { user: null, error: new Error("Service d'authentification non disponible") };
    }
    
    // Simuler une connexion réussie pour le moment
    return { user: { email }, error: null };
  } catch (error) {
    console.error('Erreur lors de la connexion:', error);
    return { user: null, error };
  }
};

const registerUser = async (email, password) => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return { user: null, error: new Error("Service d'authentification non disponible") };
    }
    
    // Simuler une inscription réussie pour le moment
    return { user: { email }, error: null };
  } catch (error) {
    console.error('Erreur lors de l\'inscription:', error);
    return { user: null, error };
  }
};

const resetPassword = async (email) => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return { success: false, error: new Error("Service d'authentification non disponible") };
    }
    
    // Simuler une réinitialisation réussie pour le moment
    return { success: true, error: null };
  } catch (error) {
    console.error("Erreur de réinitialisation:", error);
    return { success: false, error };
  }
};

const signInWithGoogle = async (idToken) => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return { user: null, error: new Error("Service d'authentification non disponible") };
    }
    
    // Simuler une connexion Google réussie pour le moment
    return { user: { email: "<EMAIL>" }, error: null };
  } catch (error) {
    console.error('Erreur lors de la connexion avec Google:', error);
    return { user: null, error };
  }
};

const signInWithFacebook = async (accessToken) => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return { user: null, error: new Error("Service d'authentification non disponible") };
    }
    
    // Simuler une connexion Facebook réussie pour le moment
    return { user: { email: "<EMAIL>" }, error: null };
  } catch (error) {
    console.error('Erreur lors de la connexion avec Facebook:', error);
    return { user: null, error };
  }
};

const signInWithApple = async (identityToken, nonce) => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return { user: null, error: new Error("Service d'authentification non disponible") };
    }
    
    // Simuler une connexion Apple réussie pour le moment
    return { user: { email: "<EMAIL>" }, error: null };
  } catch (error) {
    console.error('Erreur lors de la connexion avec Apple:', error);
    return { user: null, error };
  }
};

// Fonction pour obtenir l'utilisateur actuel
const getCurrentUser = () => {
  if (auth) {
    return auth.currentUser;
  }
  return null;
};

// Créer un objet pour les services de fallback
const fallbackServices = {
  getCategoryBudgets: async () => [],
  getGlobalBudget: async () => 0,
  calculateTotalSpent: async () => 0,
  updateCategoryBudget: async () => ({ success: false }),
  transferBudgetBetweenCategories: async () => ({ success: false }),
  calculateTotalBudget: async () => 0,
  resetAllBudgets: async () => false,
  saveGlobalBudget: async () => false,
  saveCategoryBudgets: async () => false,
  getBudgetForCategory: async (category) => ({ category, amount: 0, spent: 0 }),
  addExpense: async () => ({ success: false }),
  getUserExpenses: async () => [],
  deleteExpense: async () => ({ success: false }),
  getExpensesByDate: async () => []
};

export {
    app,
    auth,
    db, fallbackServices, getCurrentUser,
    loginUser,
    registerUser,
    resetPassword, signInWithApple, signInWithFacebook, signInWithGoogle
};

