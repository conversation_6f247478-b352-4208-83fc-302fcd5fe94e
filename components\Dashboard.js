import { Colors } from '@/constants/Colors';
import { APP_NAME } from '@/constants/Strings';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Button, Card, Dialog, Portal, ProgressBar, TextInput, Title } from 'react-native-paper';

export default function Dashboard({ navigation, userName = "Utilisateur", income = 0, expenditure = 0, balance = 0, financialGoals = [], onAddGoal }) {
  const [showGoalModal, setShowGoalModal] = useState(false);
  const [goalName, setGoalName] = useState('');
  const [goalAmount, setGoalAmount] = useState('');

  // Rendu des objectifs financiers
  const renderFinancialGoals = () => {
    if (financialGoals.length === 0) {
      return (
        <Card style={styles.emptyGoalsCard}>
          <Card.Content style={styles.emptyGoalsContainer}>
            <Text style={styles.emptyGoalsText}>Aucun objectif défini</Text>
            <Button 
              mode="contained" 
              onPress={() => setShowGoalModal(true)}
              style={styles.addGoalButton}
              color={Colors.light.tint}
            >
              Définir un objectif
            </Button>
          </Card.Content>
        </Card>
      );
    }

    return financialGoals.map((goal, index) => {
      const progress = goal.current / goal.target;
      const progressPercentage = Math.round(progress * 100);
      
      return (
        <Card key={index} style={styles.goalCard}>
          <Card.Content style={styles.goalContent}>
            <Text style={styles.goalText}>{goal.name}</Text>
            <View style={styles.goalDetails}>
              <Text style={styles.goalProgress}>{progressPercentage}% Atteint</Text>
              <Text style={styles.goalAmount}>
                {goal.current.toLocaleString()} / {goal.target.toLocaleString()} FCFA
              </Text>
            </View>
          </Card.Content>
          <ProgressBar progress={progress} color={Colors.light.tint} style={styles.progressBar} />
        </Card>
      );
    });
  };

  const handleAddGoal = () => {
    if (goalName.trim() && goalAmount.trim()) {
      const amount = parseFloat(goalAmount.replace(/\s/g, ''));
      if (!isNaN(amount) && amount > 0) {
        onAddGoal({
          name: goalName,
          target: amount,
          current: 0
        });
        setGoalName('');
        setGoalAmount('');
        setShowGoalModal(false);
      }
    }
  };

  return (
    <ScrollView style={styles.container}>
      {/* En-tête avec nom de l'app et icône de profil */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoText}>{APP_NAME}</Text>
        </View>
        <TouchableOpacity 
          style={styles.profileButton}
          onPress={() => navigation.navigate('profile')}
        >
          <Ionicons name="person-circle-outline" size={30} color={Colors.light.tint} />
        </TouchableOpacity>
      </View>

      {/* Cartes de revenus et dépenses */}
      <View style={styles.statsContainer}>
        <Card style={styles.statsCard}>
          <Card.Content>
            <View style={styles.statsHeader}>
              <Ionicons name="wallet-outline" size={20} color={Colors.light.tint} />
              <Text style={styles.statsLabel}>Budget Total</Text>
            </View>
            <Text style={styles.statsAmount}>{income.toLocaleString()} FCFA</Text>
          </Card.Content>
        </Card>

        <Card style={styles.statsCard}>
          <Card.Content>
            <View style={styles.statsHeader}>
              <Ionicons name="arrow-up-outline" size={20} color="#FF6347" />
              <Text style={[styles.statsLabel, {color: '#333'}]}>Dépenses</Text>
            </View>
            <Text style={[styles.statsAmount, {color: '#FF6347'}]}>{expenditure.toLocaleString()} FCFA</Text>
          </Card.Content>
        </Card>
      </View>

      {/* Carte de solde */}
      <Card style={styles.balanceCard}>
        <Card.Content>
          <View style={styles.balanceHeader}>
            <Text style={styles.balanceLabel}>Solde Actuel</Text>
            <Ionicons name="cash-outline" size={24} color={Colors.light.tint} />
          </View>
          <Text style={styles.balanceAmount}>{balance.toLocaleString()} FCFA</Text>
        </Card.Content>
      </Card>

      {/* Objectifs financiers */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Title style={styles.sectionTitle}>Objectifs Financiers</Title>
          <Button 
            mode="text" 
            onPress={() => setShowGoalModal(true)}
            icon="plus"
            color={Colors.light.tint}
          >
            Ajouter
          </Button>
        </View>
        {renderFinancialGoals()}
      </View>

      {/* Actions rapides */}
      <View style={styles.sectionContainer}>
        <Title style={styles.sectionTitle}>Actions Rapides</Title>
        <View style={styles.quickActionsContainer}>
          <TouchableOpacity 
            style={styles.quickAction} 
            onPress={() => navigation.navigate('index')}
          >
            <View style={[styles.actionIcon, { backgroundColor: '#E0CFFC' }]}>
              <Ionicons name="add-outline" size={24} color={Colors.light.tint} />
            </View>
            <Text style={styles.actionText}>Ajouter Dépense</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.quickAction} 
            onPress={() => navigation.navigate('index')}
          >
            <View style={[styles.actionIcon, { backgroundColor: '#D1F5D3' }]}>
              <MaterialCommunityIcons name="tag-outline" size={24} color="#4CAF50" />
            </View>
            <Text style={styles.actionText}>Gérer Budget</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.quickAction} 
            onPress={() => navigation.navigate('calendar')}
          >
            <View style={[styles.actionIcon, { backgroundColor: '#D0E6FF' }]}>
              <Ionicons name="calendar-outline" size={24} color="#2196F3" />
            </View>
            <Text style={styles.actionText}>Voir Calendrier</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.quickAction} 
            onPress={() => navigation.navigate('analysis')}
          >
            <View style={[styles.actionIcon, { backgroundColor: '#FFE0E0' }]}>
              <Ionicons name="analytics-outline" size={24} color="#F44336" />
            </View>
            <Text style={styles.actionText}>Analyser Dépenses</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Modal pour ajouter un objectif financier */}
      <Portal>
        <Dialog visible={showGoalModal} onDismiss={() => setShowGoalModal(false)}>
          <Dialog.Title>Nouvel Objectif Financier</Dialog.Title>
          <Dialog.Content>
            <TextInput
              label="Nom de l'objectif"
              value={goalName}
              onChangeText={setGoalName}
              style={styles.input}
              mode="outlined"
              theme={{ colors: { primary: Colors.light.tint } }}
            />
            <TextInput
              label="Montant cible (FCFA)"
              value={goalAmount}
              onChangeText={setGoalAmount}
              keyboardType="numeric"
              style={styles.input}
              mode="outlined"
              theme={{ colors: { primary: Colors.light.tint } }}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowGoalModal(false)} color="#888">Annuler</Button>
            <Button onPress={handleAddGoal} color={Colors.light.tint}>Ajouter</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 22,
    fontWeight: 'bold',
    color: Colors.light.tint,
  },
  profileButton: {
    padding: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statsCard: {
    width: '48%',
    borderRadius: 12,
    elevation: 2,
  },
  statsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statsLabel: {
    color: '#333',
    marginLeft: 8,
    fontSize: 14,
  },
  statsAmount: {
    color: Colors.light.tint,
    fontSize: 20,
    fontWeight: 'bold',
  },
  balanceCard: {
    borderRadius: 12,
    marginBottom: 20,
    elevation: 2,
  },
  balanceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  balanceLabel: {
    color: '#333',
    fontSize: 16,
  },
  balanceAmount: {
    color: Colors.light.tint,
    fontSize: 28,
    fontWeight: 'bold',
  },
  sectionContainer: {
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  goalCard: {
    marginBottom: 8,
    borderRadius: 8,
    elevation: 2,
  },
  goalContent: {
    marginBottom: 8,
  },
  goalText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  goalDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  goalProgress: {
    fontSize: 14,
    color: '#666',
  },
  goalAmount: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.tint,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickAction: {
    width: '48%',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
    elevation: 2,
  },
  actionIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionText: {
    fontSize: 14,
    textAlign: 'center',
    color: '#333',
  },
  emptyGoalsCard: {
    marginVertical: 10,
    borderRadius: 8,
    elevation: 2,
  },
  emptyGoalsContainer: {
    padding: 10,
    alignItems: 'center',
  },
  emptyGoalsText: {
    fontSize: 16,
    color: '#888',
    marginBottom: 10,
  },
  addGoalButton: {
    marginTop: 10,
  },
  input: {
    marginBottom: 12,
    backgroundColor: 'white',
  },
});



