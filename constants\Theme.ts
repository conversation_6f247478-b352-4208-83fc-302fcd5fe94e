import { DefaultTheme } from 'react-native-paper';
import { Colors } from './Colors';

export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: Colors.primary,
    accent: Colors.secondary,
    background: Colors.light.background,
    surface: Colors.light.cardBackground,
    text: Colors.light.text,
    error: Colors.danger,
    disabled: '#AAAAAA',
    placeholder: '#888888',
    backdrop: 'rgba(0, 0, 0, 0.5)',
  },
  roundness: 8,
  animation: {
    scale: 1.0,
  },
};

export const darkTheme = {
  ...DefaultTheme,
  dark: true,
  colors: {
    ...DefaultTheme.colors,
    primary: Colors.dark.tint,
    accent: Colors.secondary,
    background: Colors.dark.background,
    surface: Colors.dark.cardBackground,
    text: Colors.dark.text,
    error: Colors.danger,
    disabled: '#666666',
    placeholder: '#888888',
    backdrop: 'rgba(0, 0, 0, 0.7)',
  },
  roundness: 8,
  animation: {
    scale: 1.0,
  },
};

