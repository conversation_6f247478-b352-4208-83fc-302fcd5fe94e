/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = '#8A2BE2'; // Violet (couleur principale)
const tintColorDark = '#BB86FC';

export const Colors = {
  primary: '#8A2BE2', // Violet (couleur principale)
  secondary: '#FF6347', // Rouge-orange (pour les dépenses)
  success: '#4CAF50', // Vert (pour les actions positives)
  info: '#2196F3', // Bleu (pour les informations)
  warning: '#FFC107', // Jaune (pour les avertissements)
  danger: '#F44336', // Rouge (pour les erreurs)
  light: {
    text: '#333333',
    background: '#F5F5F5',
    tint: tintColorLight,
    tabIconDefault: '#CCCCCC',
    tabIconSelected: tintColorLight,
    cardBackground: '#FFFFFF',
    border: '#E0E0E0',
  },
  dark: {
    text: '#FFFFFF',
    background: '#121212',
    tint: tintColorDark,
    tabIconDefault: '#666666',
    tabIconSelected: tintColorDark,
    cardBackground: '#1E1E1E',
    border: '#333333',
  },
};



