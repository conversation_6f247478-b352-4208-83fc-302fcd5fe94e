import { Colors } from '@/constants/Colors';
import { registerUser } from '@/firebaseServices';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { Alert, SafeAreaView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { ActivityIndicator, Button, HelperText, TextInput, Title } from 'react-native-paper';

export default function RegisterScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  // Validation de l'email
  const validateEmail = () => {
    if (!email) {
      setEmailError('L\'email est requis');
      return false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setEmailError('Format d\'email invalide');
      return false;
    } else {
      setEmailError('');
      return true;
    }
  };
  
  // Validation du mot de passe
  const validatePassword = () => {
    if (!password) {
      setPasswordError('Le mot de passe est requis');
      return false;
    } else if (password.length < 6) {
      setPasswordError('Le mot de passe doit contenir au moins 6 caractères');
      return false;
    } else {
      setPasswordError('');
      return true;
    }
  };
  
  // Validation de la confirmation du mot de passe
  const validateConfirmPassword = () => {
    if (!confirmPassword) {
      setConfirmPasswordError('La confirmation du mot de passe est requise');
      return false;
    } else if (confirmPassword !== password) {
      setConfirmPasswordError('Les mots de passe ne correspondent pas');
      return false;
    } else {
      setConfirmPasswordError('');
      return true;
    }
  };
  
  // Validation de tous les champs
  const validateInputs = () => {
    const isEmailValid = validateEmail();
    const isPasswordValid = validatePassword();
    const isConfirmPasswordValid = validateConfirmPassword();
    return isEmailValid && isPasswordValid && isConfirmPasswordValid;
  };

  const handleRegister = async () => {
    if (!validateInputs()) return;
    
    setLoading(true);
    try {
      const { user, error } = await registerUser(email, password);
      
      if (error) {
        if (error.code === 'auth/email-already-in-use') {
          Alert.alert('Erreur d\'inscription', 'Cet email est déjà utilisé.');
        } else {
          Alert.alert('Erreur d\'inscription', 'Une erreur est survenue lors de l\'inscription.');
        }
      } else if (user) {
        Alert.alert(
          'Inscription réussie',
          'Votre compte a été créé avec succès.',
          [{ text: 'OK', onPress: () => router.replace('/(tabs)') }]
        );
      }
    } catch (error) {
      Alert.alert('Erreur', 'Problème lors de l\'inscription.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={Colors.light.text} />
        </TouchableOpacity>
      </View>
      
      <Title style={styles.title}>Créer un compte</Title>
      
      <TextInput
        label="Email"
        value={email}
        onChangeText={setEmail}
        onBlur={validateEmail}
        style={styles.input}
        keyboardType="email-address"
        autoCapitalize="none"
        left={<TextInput.Icon icon="email" />}
        error={!!emailError}
      />
      {emailError ? <HelperText type="error">{emailError}</HelperText> : null}
      
      <TextInput
        label="Mot de passe"
        value={password}
        onChangeText={setPassword}
        onBlur={validatePassword}
        style={styles.input}
        secureTextEntry={!showPassword}
        left={<TextInput.Icon icon="lock" />}
        right={
          <TextInput.Icon 
            icon={showPassword ? "eye-off" : "eye"} 
            onPress={() => setShowPassword(!showPassword)}
          />
        }
        error={!!passwordError}
      />
      {passwordError ? <HelperText type="error">{passwordError}</HelperText> : null}
      
      <TextInput
        label="Confirmer le mot de passe"
        value={confirmPassword}
        onChangeText={setConfirmPassword}
        onBlur={validateConfirmPassword}
        style={styles.input}
        secureTextEntry={!showConfirmPassword}
        left={<TextInput.Icon icon="lock-check" />}
        right={
          <TextInput.Icon 
            icon={showConfirmPassword ? "eye-off" : "eye"} 
            onPress={() => setShowConfirmPassword(!showConfirmPassword)}
          />
        }
        error={!!confirmPasswordError}
      />
      {confirmPasswordError ? <HelperText type="error">{confirmPasswordError}</HelperText> : null}
      
      {loading ? (
        <ActivityIndicator size="large" color={Colors.light.tint} style={styles.loader} />
      ) : (
        <Button mode="contained" style={styles.button} onPress={handleRegister}>
          S'inscrire
        </Button>
      )}
      
      <Button
        mode="text"
        style={styles.linkButton}
        onPress={() => router.push('/login')}
      >
        Déjà un compte ? Se connecter
      </Button>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 30,
    textAlign: 'center',
  },
  input: {
    marginBottom: 10,
  },
  button: {
    marginTop: 20,
    paddingVertical: 8,
  },
  loader: {
    marginTop: 20,
  },
  linkButton: {
    marginTop: 20,
  },
});
