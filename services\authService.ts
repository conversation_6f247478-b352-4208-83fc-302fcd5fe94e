import AsyncStorage from '@react-native-async-storage/async-storage';

// Types
export interface User {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
}

// Clé de stockage
const USER_STORAGE_KEY = 'user_data';

// Fonction pour enregistrer un nouvel utilisateur
export const registerUser = async (email: string, password: string) => {
  try {
    // Générer un ID unique
    const uid = Date.now().toString();
    
    // Créer un objet utilisateur
    const user: User = {
      uid,
      email,
      displayName: email.split('@')[0] // Nom d'utilisateur par défaut
    };
    
    // Stocker l'utilisateur et le mot de passe
    await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user));
    await AsyncStorage.setItem(`password_${uid}`, password);
    
    return { user, error: null };
  } catch (error) {
    console.error('Erreur lors de l\'inscription:', error);
    return { user: null, error };
  }
};

// Fonction pour connecter un utilisateur
export const loginUser = async (email: string, password: string) => {
  try {
    // Récupérer l'utilisateur
    const userJson = await AsyncStorage.getItem(USER_STORAGE_KEY);
    if (!userJson) {
      return { user: null, error: { message: 'Utilisateur non trouvé' } };
    }
    
    const user: User = JSON.parse(userJson);
    
    // Vérifier si l'email correspond
    if (user.email !== email) {
      return { user: null, error: { message: 'Email incorrect' } };
    }
    
    // Vérifier le mot de passe
    const storedPassword = await AsyncStorage.getItem(`password_${user.uid}`);
    if (storedPassword !== password) {
      return { user: null, error: { message: 'Mot de passe incorrect' } };
    }
    
    return { user, error: null };
  } catch (error) {
    console.error('Erreur lors de la connexion:', error);
    return { user: null, error };
  }
};

// Fonction pour déconnecter un utilisateur
export const logoutUser = async () => {
  try {
    await AsyncStorage.removeItem(USER_STORAGE_KEY);
    return { error: null };
  } catch (error) {
    console.error('Erreur lors de la déconnexion:', error);
    return { error };
  }
};

// Fonction pour récupérer l'utilisateur actuel
export const getCurrentUser = async () => {
  try {
    const userJson = await AsyncStorage.getItem(USER_STORAGE_KEY);
    if (userJson) {
      return JSON.parse(userJson);
    }
    return null;
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'utilisateur:', error);
    return null;
  }
};

// Fonction pour mettre à jour le profil utilisateur
export const updateUserProfile = async (displayName?: string, photoURL?: string) => {
  try {
    const userJson = await AsyncStorage.getItem(USER_STORAGE_KEY);
    if (!userJson) {
      return { error: { message: 'Utilisateur non trouvé' } };
    }
    
    const user: User = JSON.parse(userJson);
    const updatedUser: User = {
      ...user,
      displayName: displayName || user.displayName,
      photoURL: photoURL || user.photoURL
    };
    
    await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(updatedUser));
    
    return { user: updatedUser, error: null };
  } catch (error) {
    console.error('Erreur lors de la mise à jour du profil:', error);
    return { user: null, error };
  }
};

// Fonction pour réinitialiser le mot de passe (simulée)
export const resetPassword = async (email: string) => {
  try {
    const userJson = await AsyncStorage.getItem(USER_STORAGE_KEY);
    if (!userJson) {
      return { error: { message: 'Utilisateur non trouvé' } };
    }
    
    const user: User = JSON.parse(userJson);
    if (user.email !== email) {
      return { error: { message: 'Email incorrect' } };
    }
    
    // Dans une vraie application, on enverrait un email
    console.log(`Réinitialisation du mot de passe pour ${email}`);
    
    return { success: true, error: null };
  } catch (error) {
    console.error('Erreur lors de la réinitialisation du mot de passe:', error);
    return { success: false, error };
  }
};