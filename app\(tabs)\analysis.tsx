import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { APP_NAME } from '@/constants/Strings';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { useEffect, useState } from 'react';
import { Dimensions, ScrollView, StyleSheet, Text, View } from 'react-native';
import { Card, Divider, Paragraph, Title } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

const screenWidth = Dimensions.get('window').width;

export default function AnalysisScreen() {
  const [expenses, setExpenses] = useState([]);
  const [categoryData, setCategoryData] = useState([]);
  const [topCategories, setTopCategories] = useState([]);
  const [savingsTips, setSavingsTips] = useState([
    "Réduisez vos dépenses en restauration en préparant plus de repas à la maison",
    "Envisagez des alternatives moins coûteuses pour vos abonnements",
    "Établissez un budget mensuel pour chaque catégorie de dépenses",
    "Comparez les prix avant d'effectuer des achats importants"
  ]);

  useEffect(() => {
    loadExpenses();
  }, []);

  const loadExpenses = async () => {
    try {
      const expensesJson = await AsyncStorage.getItem('expenses');
      if (expensesJson) {
        const expensesData = JSON.parse(expensesJson) || [];
        setExpenses(expensesData);
        
        // Identifier les principales catégories de dépenses
        identifyTopCategories(expensesData);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des dépenses:', error);
    }
  };

  const identifyTopCategories = (expensesData) => {
    // Calculer le total par catégorie
    const categoryTotals = {};
    
    expensesData.forEach(expense => {
      const category = expense.category || 'Autres';
      if (!categoryTotals[category]) {
        categoryTotals[category] = 0;
      }
      categoryTotals[category] += parseFloat(expense.amount);
    });
    
    // Convertir en tableau et trier
    const sortedCategories = Object.keys(categoryTotals)
      .map(category => ({ 
        name: category, 
        amount: categoryTotals[category],
        percentage: 0 // Sera calculé ci-dessous
      }))
      .sort((a, b) => b.amount - a.amount);
    
    // Calculer le total des dépenses
    const totalExpenses = sortedCategories.reduce((sum, cat) => sum + cat.amount, 0);
    
    // Calculer le pourcentage pour chaque catégorie
    sortedCategories.forEach(cat => {
      cat.percentage = totalExpenses > 0 ? (cat.amount / totalExpenses) * 100 : 0;
    });
    
    setTopCategories(sortedCategories.slice(0, 5)); // Top 5 catégories
  };

  return (
    <ThemedView style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.header}>
            <View style={styles.logoContainer}>
              <Text style={styles.logoText}>{APP_NAME}</Text>
            </View>
          </View>
          
          <Title style={styles.pageTitle}>Analyse Financière</Title>
          
          {/* Résumé des dépenses */}
          <Card style={styles.card}>
            <Card.Content>
              <Title style={styles.cardTitle}>Résumé des Dépenses</Title>
              <Paragraph>
                Total des dépenses: {expenses.reduce((sum, expense) => sum + parseFloat(expense.amount), 0).toLocaleString()} FCFA
              </Paragraph>
              <Paragraph>
                Nombre de transactions: {expenses.length}
              </Paragraph>
            </Card.Content>
          </Card>
          
          {/* Top catégories de dépenses */}
          <Card style={styles.card}>
            <Card.Content>
              <Title style={styles.cardTitle}>Principales Catégories</Title>
              {topCategories.length > 0 ? (
                topCategories.map((category, index) => (
                  <View key={index} style={styles.categoryItem}>
                    <View style={styles.categoryHeader}>
                      <Text style={styles.categoryName}>{category.name}</Text>
                      <Text style={styles.categoryAmount}>{category.amount.toLocaleString()} FCFA</Text>
                    </View>
                    <View style={styles.progressBarContainer}>
                      <View 
                        style={[
                          styles.progressBar, 
                          { width: `${Math.min(category.percentage, 100)}%` }
                        ]} 
                      />
                    </View>
                    <Text style={styles.categoryPercentage}>{category.percentage.toFixed(1)}% des dépenses</Text>
                    <Divider style={styles.divider} />
                  </View>
                ))
              ) : (
                <Paragraph>Aucune dépense enregistrée</Paragraph>
              )}
            </Card.Content>
          </Card>
          
          {/* Conseils d'économie */}
          <Card style={styles.card}>
            <Card.Content>
              <Title style={styles.cardTitle}>Conseils d'Économie</Title>
              {savingsTips.map((tip, index) => (
                <View key={index} style={styles.tipItem}>
                  <Ionicons name="bulb-outline" size={24} color={Colors.light.tint} style={styles.tipIcon} />
                  <Text style={styles.tipText}>{tip}</Text>
                </View>
              ))}
            </Card.Content>
          </Card>
          
          {/* Tendances */}
          <Card style={styles.card}>
            <Card.Content>
              <Title style={styles.cardTitle}>Tendances</Title>
              <Paragraph style={styles.comingSoon}>
                Les graphiques de tendances seront disponibles prochainement !
              </Paragraph>
            </Card.Content>
          </Card>
        </ScrollView>
      </SafeAreaView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 22,
    fontWeight: 'bold',
    color: Colors.light.tint,
  },
  pageTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  card: {
    marginBottom: 16,
    borderRadius: 8,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    marginBottom: 12,
  },
  categoryItem: {
    marginBottom: 12,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
  },
  categoryAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    marginVertical: 4,
  },
  progressBar: {
    height: '100%',
    backgroundColor: Colors.light.tint,
    borderRadius: 4,
  },
  categoryPercentage: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  divider: {
    marginTop: 8,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  tipIcon: {
    marginRight: 8,
  },
  tipText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  comingSoon: {
    textAlign: 'center',
    marginTop: 20,
    fontStyle: 'italic',
  }
});
