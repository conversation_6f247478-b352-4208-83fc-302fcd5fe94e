import { getCurrentUser } from '@/firebaseConfig';
import { router } from 'expo-router';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity } from 'react-native';
import { Avatar } from 'react-native-paper';

export default function UserProfileHeader() {
  const [user, setUser] = React.useState<any>(null);
  
  React.useEffect(() => {
    const loadUser = async () => {
      const currentUser = await getCurrentUser();
      setUser(currentUser);
    };
    
    loadUser();
  }, []);
  
  if (!user) {
    return (
      <TouchableOpacity 
        style={styles.container}
        onPress={() => router.push('/login')}
      >
        <Text style={styles.loginText}>Se connecter</Text>
      </TouchableOpacity>
    );
  }
  
  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={() => router.push('/profile')}
    >
      <Avatar.Text 
        size={40} 
        label={user.email ? user.email.substring(0, 2).toUpperCase() : 'U'} 
      />
      <Text style={styles.userName}>{user.email}</Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  userName: {
    marginLeft: 10,
    fontWeight: 'bold',
  },
  loginText: {
    color: '#0a7ea4',
    fontWeight: 'bold',
  }
});