import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import React from 'react';
import { SafeAreaView, StyleSheet, Text } from 'react-native';
import { Title } from 'react-native-paper';

export default function GoalsScreen() {
  return (
    <ThemedView style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <Title style={styles.title}>Mes Objectifs</Title>
        <Text style={styles.comingSoon}>Fonctionnalité à venir prochainement!</Text>
      </SafeAreaView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    marginBottom: 20,
  },
  comingSoon: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 20,
    color: Colors.light.text,
  },
});
