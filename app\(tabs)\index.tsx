import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { APP_NAME } from '@/constants/Strings';
import { getCurrentUser } from '@/firebaseServices';
import {
  calculateTotalBudget,
  calculateTotalSpent,
  CategoryBudget,
  getCategoryBudgets,
  resetAllBudgets,
  transferBudgetBetweenCategories,
  updateCategoryBudget
} from '@/services/budgetService';
import {
  addExpense,
  deleteExpense,
  filterExpenses,
  getUserExpenses,
  resetAllExpenses
} from '@/services/expenseService';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  FlatList,
  Keyboard,
  Modal,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View
} from 'react-native';
import {
  <PERSON><PERSON>,
  Card,
  Chip,
  Divider,
  IconButton,
  Menu,
  Modal as PaperModal,
  Portal,
  ProgressBar,
  TextInput,
  Title
} from 'react-native-paper';

const windowWidth = Dimensions.get('window').width;

export default function HomeScreen() {
  // États pour le formulaire d'ajout de dépense
  const [amount, setAmount] = useState<string>('');
  const [category, setCategory] = useState<string>('');
  const [date, setDate] = useState<string>(new Date().toISOString().split('T')[0].split('-').reverse().join('/'));
  
  // États pour le budget
  const [categoryBudgets, setCategoryBudgets] = useState<CategoryBudget[]>([]);
  const [totalBudget, setTotalBudget] = useState<number>(0);
  const [totalSpent, setTotalSpent] = useState<number>(0);
  const [showBudgetModal, setShowBudgetModal] = useState<boolean>(false);
  const [newBudgetAmount, setNewBudgetAmount] = useState<string>('');
  const [selectedBudgetCategory, setSelectedBudgetCategory] = useState<string>('');
  
  // États pour le transfert de budget
  const [showTransferModal, setShowTransferModal] = useState<boolean>(false);
  const [transferAmount, setTransferAmount] = useState<string>('');
  const [fromCategory, setFromCategory] = useState<string>('');
  const [toCategory, setToCategory] = useState<string>('');
  
  // États pour les dépenses
  const [expenses, setExpenses] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  
  // État pour l'utilisateur
  const [user, setUser] = useState<any>(null);
  
  // Catégories prédéfinies
  const categories = ['Alimentation', 'Transport', 'Logement', 'Loisirs', 'Santé', 'Éducation', 'Autres'];

  // Référence pour le menu d'options de dépense
  const [menuVisible, setMenuVisible] = useState<boolean>(false);
  const [selectedExpenseId, setSelectedExpenseId] = useState<string | null>(null);

  // Ajoutez ces états pour le filtre avancé
  const [showFilterModal, setShowFilterModal] = useState<boolean>(false);
  const [filters, setFilters] = useState({
    startDate: '',
    endDate: '',
    minAmount: '',
    maxAmount: '',
    categories: [] as string[]
  });
  const [filteredExpenses, setFilteredExpenses] = useState<any[]>([]);
  const [isFiltering, setIsFiltering] = useState<boolean>(false);

  useEffect(() => {
    loadUser();
    loadBudgets();
    loadExpenses();
  }, []);

  const loadUser = async () => {
    try {
      const currentUser = await getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('Erreur lors du chargement de l\'utilisateur:', error);
    }
  };

  const loadBudgets = async () => {
    try {
      const budgets = await getCategoryBudgets();
      setCategoryBudgets(budgets);
      
      const total = await calculateTotalBudget();
      setTotalBudget(total);
      
      const spent = await calculateTotalSpent();
      setTotalSpent(spent);
    } catch (error) {
      console.error('Erreur lors du chargement des budgets:', error);
    }
  };

  const loadExpenses = async () => {
    setLoading(true);
    try {
      const userExpenses = await getUserExpenses();
      setExpenses(userExpenses);
    } catch (error) {
      console.error('Erreur lors du chargement des dépenses:', error);
      Alert.alert('Erreur', 'Impossible de charger les dépenses.');
    } finally {
      setLoading(false);
    }
  };

  const handleAddExpense = async () => {
    const parsedAmount = parseFloat(amount);
    if (!isNaN(parsedAmount) && parsedAmount > 0 && category.trim() && date.trim()) {
      // Vérifier si le budget de la catégorie est suffisant
      const categoryBudget = categoryBudgets.find(b => b.category === category);
      
      if (categoryBudget && categoryBudget.spent + parsedAmount > categoryBudget.amount) {
        Alert.alert(
          'Dépassement de budget',
          `Cette dépense dépasse le budget alloué pour ${category}. Voulez-vous continuer?`,
          [
            { text: 'Annuler', style: 'cancel' },
            { 
              text: 'Continuer', 
              style: 'destructive',
              onPress: () => addExpenseConfirmed(parsedAmount)
            }
          ]
        );
      } else {
        addExpenseConfirmed(parsedAmount);
      }
    } else {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs correctement.');
    }
  };

  const addExpenseConfirmed = async (parsedAmount: number) => {
    setLoading(true);
    try {
      // Convertir DD/MM/YYYY en YYYY-MM-DD
      const [day, month, year] = date.split('/');
      const formattedDate = `${year}-${month}-${day}`;
      
      await addExpense(parsedAmount, category, formattedDate);
      
      setAmount('');
      setCategory('');
      setDate(new Date().toISOString().split('T')[0].split('-').reverse().join('/'));
      
      // Recharger les budgets et les dépenses
      await loadBudgets();
      await loadExpenses();
      
      Keyboard.dismiss();
      Alert.alert('Succès', 'Dépense ajoutée avec succès');
    } catch (error) {
      console.error('Erreur lors de l\'ajout de la dépense:', error);
      Alert.alert('Erreur', 'Impossible d\'ajouter la dépense.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteExpense = async (id: string) => {
    Alert.alert(
      'Confirmation',
      'Êtes-vous sûr de vouloir supprimer cette dépense ?',
      [
        { text: 'Annuler', style: 'cancel' },
        { 
          text: 'Supprimer', 
          style: 'destructive',
          onPress: async () => {
            setLoading(true);
            try {
              await deleteExpense(id);
              await loadBudgets();
              await loadExpenses();
              Alert.alert('Succès', 'Dépense supprimée avec succès');
            } catch (error) {
              console.error('Erreur lors de la suppression de la dépense:', error);
              Alert.alert('Erreur', 'Impossible de supprimer la dépense.');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const handleResetAllExpenses = () => {
    Alert.alert(
      'Confirmation',
      'Êtes-vous sûr de vouloir réinitialiser toutes les dépenses ? Cette action est irréversible.',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Réinitialiser',
          style: 'destructive',
          onPress: async () => {
            setLoading(true);
            try {
              await resetAllExpenses();
              await loadBudgets();
              await loadExpenses();
              Alert.alert('Succès', 'Toutes les dépenses ont été réinitialisées.');
            } catch (error) {
              console.error('Erreur lors de la réinitialisation des dépenses:', error);
              Alert.alert('Erreur', 'Impossible de réinitialiser les dépenses.');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const handleUpdateCategoryBudget = async () => {
    const parsedAmount = parseFloat(newBudgetAmount);
    if (!isNaN(parsedAmount) && parsedAmount >= 0 && selectedBudgetCategory) {
      try {
        await updateCategoryBudget(selectedBudgetCategory, parsedAmount);
        await loadBudgets();
        setShowBudgetModal(false);
        setNewBudgetAmount('');
        setSelectedBudgetCategory('');
        Alert.alert('Succès', 'Budget de catégorie mis à jour avec succès');
      } catch (error) {
        console.error('Erreur lors de la mise à jour du budget de catégorie:', error);
        Alert.alert('Erreur', 'Impossible de mettre à jour le budget de catégorie.');
      }
    } else {
      Alert.alert('Erreur', 'Veuillez entrer un montant valide.');
    }
  };

  const handleTransferBudget = async () => {
    const parsedAmount = parseFloat(transferAmount);
    if (!isNaN(parsedAmount) && parsedAmount > 0 && fromCategory && toCategory && fromCategory !== toCategory) {
      try {
        await transferBudgetBetweenCategories(fromCategory, toCategory, parsedAmount);
        await loadBudgets();
        setShowTransferModal(false);
        setTransferAmount('');
        setFromCategory('');
        setToCategory('');
        Alert.alert('Succès', 'Budget transféré avec succès');
      } catch (error) {
        console.error('Erreur lors du transfert de budget:', error);
        Alert.alert('Erreur', 'Impossible de transférer le budget.');
      }
    } else {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs correctement.');
    }
  };

  const renderExpenseItem = ({ item }: { item: any }) => (
    <Card style={styles.expenseCard}>
      <Card.Content>
        <View style={styles.expenseHeader}>
          <Chip icon="tag" style={styles.categoryChip}>{item.category}</Chip>
          <Text style={styles.expenseAmount}>{item.amount} FCFA</Text>
          <IconButton
            icon="dots-vertical"
            size={20}
            onPress={() => {
              setSelectedExpenseId(item.id);
              setMenuVisible(true);
            }}
          />
        </View>
        <Divider style={styles.divider} />
        <Text style={styles.expenseDate}>
          <Ionicons name="calendar-outline" size={16} color={Colors.light.text} /> {item.date.split('-').reverse().join('/')}
        </Text>
      </Card.Content>
      <Menu
        visible={menuVisible && selectedExpenseId === item.id}
        onDismiss={() => setMenuVisible(false)}
        anchor={{ x: windowWidth - 40, y: 50 }}
      >
        <Menu.Item 
          onPress={() => {
            setMenuVisible(false);
            handleDeleteExpense(item.id);
          }} 
          title="Supprimer" 
          leadingIcon="delete"
        />
      </Menu>
    </Card>
  );

  const renderCategoryBudgetItem = ({ item }: { item: CategoryBudget }) => {
    const progress = item.amount > 0 ? item.spent / item.amount : 0;
    const isOverBudget = item.spent > item.amount && item.amount > 0;
    
    return (
      <Card style={styles.budgetCard}>
        <Card.Content>
          <View style={styles.budgetCardHeader}>
            <Chip icon="tag" style={styles.categoryChip}>{item.category}</Chip>
            <Text style={styles.budgetAmount}>{item.amount} FCFA</Text>
          </View>
          
          <View style={styles.budgetDetails}>
            <Text style={styles.budgetDetailText}>
              Dépensé: <Text style={styles.spentText}>{item.spent} FCFA</Text>
            </Text>
            {item.amount > 0 && (
              <Text style={styles.budgetDetailText}>
                Restant: <Text style={[
                  styles.remainingText, 
                  isOverBudget ? styles.overBudget : null
                ]}>
                  {Math.max(0, item.amount - item.spent)} FCFA
                </Text>
              </Text>
            )}
          </View>
          
          {item.amount > 0 ? (
            <ProgressBar 
              progress={Math.min(progress, 1)} 
              color={isOverBudget ? 'red' : Colors.light.tint} 
              style={styles.progressBar} 
            />
          ) : (
            <Text style={styles.noBudgetText}>Aucun budget défini</Text>
          )}
        </Card.Content>
      </Card>
    );
  };

  // Ajoutez cette fonction pour appliquer les filtres
  const applyFilters = async () => {
    setLoading(true);
    try {
      // Valider les dates
      let startDateISO = '';
      let endDateISO = '';
      
      if (filters.startDate) {
        startDateISO = formatDateToISO(filters.startDate);
        if (!startDateISO) {
          Alert.alert('Format de date invalide', 'Veuillez entrer la date de début au format JJ/MM/AAAA');
          setLoading(false);
          return;
        }
      }
      
      if (filters.endDate) {
        endDateISO = formatDateToISO(filters.endDate);
        if (!endDateISO) {
          Alert.alert('Format de date invalide', 'Veuillez entrer la date de fin au format JJ/MM/AAAA');
          setLoading(false);
          return;
        }
      }
      
      // Valider les montants
      let minAmount: number | undefined = undefined;
      let maxAmount: number | undefined = undefined;
      
      if (filters.minAmount) {
        minAmount = parseFloat(filters.minAmount);
        if (isNaN(minAmount)) {
          Alert.alert('Montant invalide', 'Le montant minimum doit être un nombre');
          setLoading(false);
          return;
        }
      }
      
      if (filters.maxAmount) {
        maxAmount = parseFloat(filters.maxAmount);
        if (isNaN(maxAmount)) {
          Alert.alert('Montant invalide', 'Le montant maximum doit être un nombre');
          setLoading(false);
          return;
        }
      }
      
      // Vérifier que min <= max
      if (minAmount !== undefined && maxAmount !== undefined && minAmount > maxAmount) {
        Alert.alert('Montants invalides', 'Le montant minimum doit être inférieur ou égal au montant maximum');
        setLoading(false);
        return;
      }
      
      const filterParams = {
        startDate: startDateISO,
        endDate: endDateISO,
        minAmount,
        maxAmount,
        categories: filters.categories.length > 0 ? filters.categories : undefined
      };
      
      console.log('Filtres appliqués:', filterParams);
      
      const results = await filterExpenses(filterParams);
      setFilteredExpenses(results);
      setIsFiltering(true);
      setShowFilterModal(false);
    } catch (error) {
      console.error('Erreur lors du filtrage des dépenses:', error);
      Alert.alert('Erreur', 'Impossible de filtrer les dépenses. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour réinitialiser les filtres
  const resetFilters = () => {
    setFilters({
      startDate: '',
      endDate: '',
      minAmount: '',
      maxAmount: '',
      categories: []
    });
    setIsFiltering(false);
    setFilteredExpenses([]);
  };

  // Fonction pour convertir DD/MM/YYYY en YYYY-MM-DD
  const formatDateToISO = (dateString: string) => {
    if (!dateString || !dateString.includes('/')) return '';
    const parts = dateString.split('/');
    if (parts.length !== 3) return '';
    
    const day = parts[0].padStart(2, '0');
    const month = parts[1].padStart(2, '0');
    const year = parts[2].length === 2 ? `20${parts[2]}` : parts[2];
    
    return `${year}-${month}-${day}`;
  };

  // Fonction pour convertir YYYY-MM-DD en DD/MM/YYYY
  const formatDateToDisplay = (dateString: string) => {
    const [year, month, day] = dateString.split('-');
    return `${day}/${month}/${year}`;
  };

  // Ajoutez cette fonction pour réinitialiser complètement l'état de l'application
  const resetAppState = async () => {
    setLoading(true);
    try {
      // Réinitialiser les budgets (supprimer toutes les catégories)
      await resetAllBudgets();
      
      // Réinitialiser les dépenses
      await resetAllExpenses();
      
      // Réinitialiser explicitement les états locaux
      setExpenses([]);
      setCategoryBudgets([]); // Tableau vide pour supprimer toutes les catégories
      setFilteredExpenses([]);
      setTotalBudget(0);
      setTotalSpent(0);
      
      // Recharger les données fraîches
      await loadBudgets();
      await loadExpenses();
      
      Alert.alert('Succès', 'Tous les budgets et dépenses ont été réinitialisés.');
    } catch (error) {
      console.error('Erreur lors de la réinitialisation des budgets et dépenses:', error);
      Alert.alert('Erreur', 'Impossible de réinitialiser les budgets et dépenses.');
    } finally {
      setLoading(false);
    }
  };

  // Modifiez la fonction handleResetAllBudgets pour utiliser resetAppState
  const handleResetAllBudgets = () => {
    Alert.alert(
      'Confirmation',
      'Êtes-vous sûr de vouloir réinitialiser tous les budgets et dépenses ? Cette action est irréversible.',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Réinitialiser',
          style: 'destructive',
          onPress: resetAppState
        }
      ]
    );
  };

  return (
    <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollViewContent}>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <ThemedView style={styles.container}>
          <SafeAreaView style={styles.safeArea}>
            {/* Header avec logo et bouton de connexion */}
            <View style={styles.header}>
              <View style={styles.logoContainer}>
                <Text style={styles.logoText}>{APP_NAME}</Text>
              </View>
              <TouchableOpacity 
                style={styles.loginButton}
                onPress={() => router.push('/login')}
              >
                <Ionicons name={user ? "person" : "person-outline"} size={24} color={Colors.light.tint} />
              </TouchableOpacity>
            </View>
            
            {/* Section du budget total */}
            <View style={styles.totalBudgetSection}>
              <View style={styles.totalBudgetHeader}>
                <Title style={styles.sectionTitle}>Budget Total</Title>
                <View style={styles.budgetActions}>
                  <IconButton
                    icon="plus"
                    size={20}
                    onPress={() => {
                      setSelectedBudgetCategory('');
                      setNewBudgetAmount('');
                      setShowBudgetModal(true);
                    }}
                  />
                  <IconButton
                    icon="swap-horizontal"
                    size={20}
                    onPress={() => setShowTransferModal(true)}
                  />
                  <IconButton
                    icon="refresh"
                    size={20}
                    onPress={handleResetAllBudgets}
                    color={Colors.light.tint}
                  />
                </View>
              </View>
              
              <View style={styles.totalBudgetDisplay}>
                <Text style={styles.budgetLabel}>Budget total:</Text>
                <Text style={styles.totalBudgetAmount}>{totalBudget} FCFA</Text>
                
                <View style={styles.budgetSummary}>
                  <Text style={styles.budgetSummaryText}>
                    Dépensé: <Text style={styles.spentText}>{totalSpent} FCFA</Text>
                  </Text>
                  <Text style={styles.budgetSummaryText}>
                    Restant: <Text style={[
                      styles.remainingText, 
                      totalBudget - totalSpent < 1000 ? styles.budgetLow : null
                    ]}>
                      {Math.max(0, totalBudget - totalSpent)} FCFA
                    </Text>
                  </Text>
                </View>
                
                <ProgressBar 
                  progress={totalBudget > 0 ? Math.min(totalSpent / totalBudget, 1) : 0} 
                  color={totalSpent > totalBudget ? 'red' : Colors.light.tint} 
                  style={styles.totalBudgetProgress} 
                />
              </View>
            </View>
            
            {/* Liste des budgets par catégorie */}
            <View style={styles.categoryBudgetsSection}>
              <Title style={styles.sectionTitle}>Budgets par catégorie</Title>
              
              {categoryBudgets.length > 0 ? (
                <FlatList
                  data={categoryBudgets}
                  renderItem={renderCategoryBudgetItem}
                  keyExtractor={(item) => item.category}
                  style={styles.categoryBudgetsList}
                  nestedScrollEnabled={true}
                  scrollEnabled={false}
                />
              ) : (
                <View style={styles.emptyBudgetsContainer}>
                  <Text style={styles.noBudgets}>Aucun budget défini. Ajoutez un budget pour commencer.</Text>
                  <Button 
                    mode="outlined" 
                    onPress={() => {
                      setSelectedBudgetCategory('');
                      setNewBudgetAmount('');
                      setShowBudgetModal(true);
                    }}
                    style={styles.addBudgetButton}
                    icon="plus"
                  >
                    Ajouter un budget
                  </Button>
                </View>
              )}
            </View>
            
            {/* Section d'ajout de dépense */}
            <View style={styles.addExpenseSection}>
              <Title style={styles.sectionTitle}>Ajouter une dépense</Title>
              
              <TextInput
                label="Montant"
                value={amount}
                onChangeText={setAmount}
                keyboardType="numeric"
                mode="outlined"
                style={styles.input}
                right={<TextInput.Affix text="FCFA" />}
              />
              
              <View style={styles.categoriesContainer}>
                <Text style={styles.inputLabel}>Catégorie:</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesScroll}>
                  {categories.map((cat) => (
                    <Chip
                      key={cat}
                      selected={category === cat}
                      onPress={() => setCategory(cat)}
                      style={[styles.categoryChip, category === cat ? styles.selectedCategoryChip : null]}
                      textStyle={category === cat ? styles.selectedCategoryText : null}
                    >
                      {cat}
                    </Chip>
                  ))}
                </ScrollView>
              </View>
              
              <TextInput
                label="Date (JJ/MM/AAAA)"
                value={date}
                onChangeText={setDate}
                mode="outlined"
                style={styles.input}
                right={<TextInput.Icon icon="calendar" />}
              />
              
              <Button 
                mode="contained" 
                onPress={handleAddExpense}
                style={styles.addButton}
                icon="plus"
                loading={loading}
                disabled={loading}
              >
                Ajouter la dépense
              </Button>
            </View>

            {/* Liste des dépenses */}
            <View style={styles.expensesListSection}>
              <View style={styles.expensesHeader}>
                <Title style={styles.sectionTitle}>Dernières dépenses</Title>
                <View style={styles.expensesActions}>
                  {isFiltering && (
                    <Chip 
                      icon="filter-remove" 
                      onPress={resetFilters}
                      style={styles.filterChip}
                    >
                      Filtres actifs
                    </Chip>
                  )}
                  <IconButton
                    icon="filter-variant"
                    size={24}
                    onPress={() => setShowFilterModal(true)}
                    color={Colors.light.tint}
                  />
                  {expenses.length > 0 && (
                    <Button 
                      mode="outlined" 
                      onPress={handleResetAllExpenses}
                      style={styles.resetButton}
                      icon="delete-sweep"
                      textColor={Colors.light.tint}
                    >
                      Réinitialiser
                    </Button>
                  )}
                </View>
              </View>
              
              {loading ? (
                <ActivityIndicator size="large" color={Colors.light.tint} style={styles.loader} />
              ) : (
                <>
                  {isFiltering && (
                    <View style={styles.filterIndicator}>
                      <Chip 
                        icon="filter-remove" 
                        onPress={resetFilters}
                        style={styles.filterChip}
                      >
                        {filteredExpenses.length} résultat(s) trouvé(s)
                      </Chip>
                    </View>
                  )}
                  
                  {(isFiltering ? filteredExpenses.length : expenses.length) > 0 ? (
                    <FlatList
                      data={isFiltering ? filteredExpenses : expenses}
                      renderItem={renderExpenseItem}
                      keyExtractor={(item) => item.id}
                      style={styles.expensesList}
                      nestedScrollEnabled={true}
                      scrollEnabled={false}
                    />
                  ) : (
                    <Text style={styles.noExpenses}>
                      {isFiltering ? 'Aucune dépense ne correspond aux filtres.' : 'Aucune dépense enregistrée.'}
                    </Text>
                  )}
                </>
              )}
            </View>
          </SafeAreaView>

          {/* Modal pour ajouter/modifier un budget de catégorie */}
          <Modal
            visible={showBudgetModal}
            transparent={true}
            animationType="slide"
            onRequestClose={() => setShowBudgetModal(false)}
          >
            <TouchableWithoutFeedback onPress={() => setShowBudgetModal(false)}>
              <View style={styles.modalOverlay}>
                <TouchableWithoutFeedback onPress={e => e.stopPropagation()}>
                  <View style={styles.modalContent}>
                    <Title style={styles.modalTitle}>
                      {selectedBudgetCategory 
                        ? `Modifier le budget: ${selectedBudgetCategory}` 
                        : 'Ajouter un nouveau budget'}
                    </Title>
                    
                    {!selectedBudgetCategory && (
                      <View style={styles.categoriesContainer}>
                        <Text style={styles.inputLabel}>Catégorie:</Text>
                        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesScroll}>
                          {categories.map((cat) => (
                            <Chip
                              key={cat}
                              selected={selectedBudgetCategory === cat}
                              onPress={() => setSelectedBudgetCategory(cat)}
                              style={[styles.categoryChip, selectedBudgetCategory === cat ? styles.selectedCategoryChip : null]}
                              textStyle={selectedBudgetCategory === cat ? styles.selectedCategoryText : null}
                            >
                              {cat}
                            </Chip>
                          ))}
                        </ScrollView>
                      </View>
                    )}
                    
                    <TextInput
                      label="Montant du budget"
                      value={newBudgetAmount}
                      onChangeText={setNewBudgetAmount}
                      keyboardType="numeric"
                      mode="outlined"
                      style={styles.input}
                      right={<TextInput.Affix text="FCFA" />}
                    />
                    
                    <View style={styles.modalButtons}>
                      <Button 
                        mode="outlined" 
                        onPress={() => setShowBudgetModal(false)}
                        style={styles.modalButton}
                      >
                        Annuler
                      </Button>
                      <Button 
                        mode="contained" 
                        onPress={handleUpdateCategoryBudget}
                        style={styles.modalButton}
                        disabled={!selectedBudgetCategory || !newBudgetAmount}
                      >
                        Enregistrer
                      </Button>
                    </View>
                  </View>
                </TouchableWithoutFeedback>
              </View>
            </TouchableWithoutFeedback>
          </Modal>

          {/* Modal pour transférer du budget entre catégories */}
          <Modal
            visible={showTransferModal}
            transparent={true}
            animationType="slide"
            onRequestClose={() => setShowTransferModal(false)}
          >
            <TouchableWithoutFeedback onPress={() => setShowTransferModal(false)}>
              <View style={styles.modalOverlay}>
                <TouchableWithoutFeedback onPress={e => e.stopPropagation()}>
                  <View style={styles.modalContent}>
                    <Title style={styles.modalTitle}>Transférer du budget</Title>
                    
                    <View style={styles.categoriesContainer}>
                      <Text style={styles.inputLabel}>De la catégorie:</Text>
                      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesScroll}>
                        {categoryBudgets.map((budget) => (
                          <Chip
                            key={`from-${budget.category}`}
                            selected={fromCategory === budget.category}
                            onPress={() => setFromCategory(budget.category)}
                            style={[styles.categoryChip, fromCategory === budget.category ? styles.selectedCategoryChip : null]}
                            textStyle={fromCategory === budget.category ? styles.selectedCategoryText : null}
                            disabled={budget.amount <= 0}
                          >
                            {budget.category} ({budget.amount} FCFA)
                          </Chip>
                        ))}
                      </ScrollView>
                    </View>
                    
                    <View style={styles.categoriesContainer}>
                      <Text style={styles.inputLabel}>Vers la catégorie:</Text>
                      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesScroll}>
                        {categories.map((cat) => (
                          <Chip
                            key={`to-${cat}`}
                            selected={toCategory === cat}
                            onPress={() => setToCategory(cat)}
                            style={[styles.categoryChip, toCategory === cat ? styles.selectedCategoryChip : null]}
                            textStyle={toCategory === cat ? styles.selectedCategoryText : null}
                            disabled={fromCategory === cat}
                          >
                            {cat}
                          </Chip>
                        ))}
                      </ScrollView>
                    </View>
                    
                    <TextInput
                      label="Montant à transférer"
                      value={transferAmount}
                      onChangeText={setTransferAmount}
                      keyboardType="numeric"
                      mode="outlined"
                      style={styles.input}
                      right={<TextInput.Affix text="FCFA" />}
                    />
                    
                    <View style={styles.modalButtons}>
                      <Button 
                        mode="outlined" 
                        onPress={() => setShowTransferModal(false)}
                        style={styles.modalButton}
                      >
                        Annuler
                      </Button>
                      <Button 
                        mode="contained" 
                        onPress={handleTransferBudget}
                        style={styles.modalButton}
                        disabled={!fromCategory || !toCategory || !transferAmount}
                      >
                        Transférer
                      </Button>
                    </View>
                  </View>
                </TouchableWithoutFeedback>
              </View>
            </TouchableWithoutFeedback>
          </Modal>

          {/* Modal de filtre avancé */}
          <Portal>
            <PaperModal
              visible={showFilterModal}
              onDismiss={() => setShowFilterModal(false)}
              contentContainerStyle={styles.modalContent}
            >
              <Title style={styles.modalTitle}>Filtres avancés</Title>
              
              <View style={styles.filterSection}>
                <Text style={styles.filterLabel}>Période</Text>
                <View style={styles.dateFilterRow}>
                  <TextInput
                    label="Date début"
                    value={filters.startDate}
                    onChangeText={(text) => setFilters({...filters, startDate: text})}
                    mode="outlined"
                    placeholder="JJ/MM/AAAA"
                    style={styles.dateInput}
                  />
                  <TextInput
                    label="Date fin"
                    value={filters.endDate}
                    onChangeText={(text) => setFilters({...filters, endDate: text})}
                    mode="outlined"
                    placeholder="JJ/MM/AAAA"
                    style={styles.dateInput}
                  />
                </View>
              </View>
              
              <View style={styles.filterSection}>
                <Text style={styles.filterLabel}>Montant</Text>
                <View style={styles.dateFilterRow}>
                  <TextInput
                    label="Minimum"
                    value={filters.minAmount}
                    onChangeText={(text) => setFilters({...filters, minAmount: text})}
                    keyboardType="numeric"
                    mode="outlined"
                    style={styles.dateInput}
                  />
                  <TextInput
                    label="Maximum"
                    value={filters.maxAmount}
                    onChangeText={(text) => setFilters({...filters, maxAmount: text})}
                    keyboardType="numeric"
                    mode="outlined"
                    style={styles.dateInput}
                  />
                </View>
              </View>
              
              <View style={styles.filterSection}>
                <Text style={styles.filterLabel}>Catégories</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesScroll}>
                  {categories.map((cat) => (
                    <Chip
                      key={`filter-${cat}`}
                      selected={filters.categories.includes(cat)}
                      onPress={() => {
                        if (filters.categories.includes(cat)) {
                          setFilters({
                            ...filters, 
                            categories: filters.categories.filter(c => c !== cat)
                          });
                        } else {
                          setFilters({
                            ...filters,
                            categories: [...filters.categories, cat]
                          });
                        }
                      }}
                      style={styles.categoryChip}
                    >
                      {cat}
                    </Chip>
                  ))}
                </ScrollView>
              </View>
              
              <View style={styles.modalButtons}>
                <Button 
                  mode="outlined" 
                  onPress={() => {
                    resetFilters();
                    setShowFilterModal(false);
                  }}
                  style={styles.modalButton}
                >
                  Réinitialiser
                </Button>
                <Button 
                  mode="contained" 
                  onPress={applyFilters}
                  style={styles.modalButton}
                >
                  Appliquer
                </Button>
              </View>
            </PaperModal>
          </Portal>
        </ThemedView>
      </TouchableWithoutFeedback>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
    paddingVertical: 5,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.tint,
    marginLeft: 5,
  },
  loginButton: {
    padding: 5,
  },
  budgetSection: {
    backgroundColor: Colors.light.background,
    padding: 15,
    borderRadius: 10,
    marginBottom: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  budgetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  budgetInputContainer: {
    marginTop: 10,
  },
  budgetInput: {
    marginBottom: 10,
  },
  budgetButton: {
    marginTop: 5,
  },
  budgetDisplay: {
    marginTop: 5,
  },
  budgetLabel: {
    fontSize: 16,
    color: Colors.light.text,
    marginBottom: 5,
  },
  budgetAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.tint,
  },
  budgetLow: {
    color: 'red',
  },
  addExpenseSection: {
    backgroundColor: Colors.light.background,
    padding: 15,
    borderRadius: 10,
    marginBottom: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.text,
  },
  input: {
    marginBottom: 15,
  },
  inputLabel: {
    fontSize: 16,
    marginBottom: 10,
    color: Colors.light.text,
  },
  categoriesContainer: {
    marginBottom: 15,
  },
  categoriesScroll: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  categoryChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  selectedCategoryChip: {
    backgroundColor: Colors.light.tint,
  },
  selectedCategoryText: {
    color: 'white',
  },
  addButton: {
    marginTop: 10,
  },
  expensesListSection: {
    backgroundColor: Colors.light.background,
    padding: 15,
    borderRadius: 10,
    marginBottom: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  expensesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  resetButton: {
    borderColor: Colors.light.tint,
  },
  expensesList: {
    marginTop: 10,
  },
  expenseCard: {
    marginBottom: 10,
  },
  expenseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryChip: {
    backgroundColor: '#f0f0f0',
  },
  expenseAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.text,
  },
  divider: {
    marginVertical: 10,
  },
  expenseDate: {
    color: Colors.light.text,
    opacity: 0.7,
  },
  noExpenses: {
    textAlign: 'center',
    marginTop: 20,
    color: Colors.light.text,
    opacity: 0.7,
  },
  loader: {
    marginTop: 20,
  },
  totalBudgetSection: {
    backgroundColor: Colors.light.background,
    padding: 15,
    borderRadius: 10,
    marginBottom: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  totalBudgetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  budgetActions: {
    flexDirection: 'row',
    gap: 10,
  },
  totalBudgetDisplay: {
    marginTop: 5,
  },
  budgetSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  budgetSummaryText: {
    fontSize: 16,
    color: Colors.light.text,
  },
  spentText: {
    color: Colors.light.tint,
  },
  remainingText: {
    color: Colors.light.tint,
  },
  budgetLow: {
    color: 'red',
  },
  totalBudgetProgress: {
    height: 10,
    borderRadius: 5,
  },
  categoryBudgetsSection: {
    backgroundColor: Colors.light.background,
    padding: 15,
    borderRadius: 10,
    marginBottom: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  categoryBudgetsList: {
    marginTop: 10,
  },
  budgetCard: {
    marginBottom: 10,
  },
  budgetCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  budgetProgressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  budgetProgress: {
    flex: 1,
    marginRight: 10,
  },
  budgetAmounts: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  spentAmount: {
    fontSize: 16,
    color: Colors.light.text,
  },
  totalAmount: {
    fontSize: 16,
    color: Colors.light.tint,
  },
  noBudgets: {
    textAlign: 'center',
    marginTop: 20,
    color: Colors.light.text,
    opacity: 0.7,
  },
  emptyBudgetsContainer: {
    padding: 20,
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginVertical: 10,
  },
  addBudgetButton: {
    marginTop: 10,
  },
  // Styles pour les modals
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: Colors.light.background,
    borderRadius: 10,
    padding: 20,
    width: '100%',
    maxWidth: 500,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 5,
  },
  filterSection: {
    marginBottom: 15,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  dateFilterRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dateInput: {
    flex: 1,
    marginHorizontal: 5,
  },
  expensesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  expensesActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterChip: {
    marginRight: 8,
  },
  filterIndicator: {
    marginVertical: 10,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  emptyChartContainer: {
    height: 220,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginVertical: 10,
  },
  emptyChartText: {
    fontSize: 16,
    color: '#888',
  },
  noBudgetText: {
    fontSize: 14,
    color: '#888',
    fontStyle: 'italic',
    marginTop: 8,
  },
});










