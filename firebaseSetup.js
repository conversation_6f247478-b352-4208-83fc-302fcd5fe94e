// firebaseSetup.js - Point unique d'initialisation de Firebase
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getApp, getApps, initializeApp } from 'firebase/app';
import { getAuth, getReactNativePersistence, initializeAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

// Configuration Firebase
const firebaseConfig = {
  apiKey: "AIzaSyDZYVxe-GZ1xWLl2UYVu_hmHCjZM9lNxTs",
  authDomain: "mboabudget.firebaseapp.com",
  projectId: "mboabudget",
  storageBucket: "mboabudget.appspot.com",
  messagingSenderId: "1045282978405",
  appId: "1:1045282978405:web:c9e9f1a7a4d8f3a5a5a5a5"
};

// Initialiser Firebase une seule fois
let app;
let auth;
let db;

// Fonction pour initialiser Firebase
const initializeFirebase = () => {
  try {
    if (!getApps().length) {
      console.log("Initializing new Firebase app");
      app = initializeApp(firebaseConfig);
    } else {
      console.log("Using existing Firebase app");
      app = getApp();
    }

    // Initialiser Auth avec AsyncStorage persistence
    try {
      auth = getAuth(app);
      console.log("Using existing auth instance");
    } catch (error) {
      console.log("Initializing new auth instance with AsyncStorage");
      auth = initializeAuth(app, {
        persistence: getReactNativePersistence(AsyncStorage)
      });
    }

    // Initialiser Firestore
    db = getFirestore(app);

    console.log("Firebase setup completed successfully");
    return { app, auth, db };
  } catch (error) {
    console.error("Firebase setup error:", error);
    return { app: null, auth: null, db: null };
  }
};

// Exporter les instances et la fonction d'initialisation
export { initializeFirebase };
