// services/expenseService.js

// Type pour une dépense
export class Expense {
  constructor(id, amount, category, date) {
    this.id = id;
    this.amount = amount;
    this.category = category;
    this.date = date;
  }
}

// Fonction pour ajouter une dépense
export const addExpense = async (amount, category, date) => {
  try {
    // Simuler l'ajout d'une dépense
    console.log(`Ajout d'une dépense: ${amount} FCFA dans ${category} le ${date}`);
    return { success: true };
  } catch (error) {
    console.error("Erreur lors de l'ajout de la dépense:", error);
    return { success: false };
  }
};

// Fonction pour obtenir les dépenses de l'utilisateur
export const getUserExpenses = async () => {
  try {
    // Simuler des dépenses
    return [
      new Expense("1", 5000, "Alimentation", "2023-06-15"),
      new Expense("2", 2000, "Transport", "2023-06-16"),
      new Expense("3", 10000, "Loyer", "2023-06-01")
    ];
  } catch (error) {
    console.error("Erreur lors de la récupération des dépenses:", error);
    return [];
  }
};

// Fonction pour obtenir les dépenses par date
export const getExpensesByDate = async (date) => {
  try {
    // Simuler des dépenses pour une date spécifique
    const allExpenses = await getUserExpenses();
    return allExpenses.filter(expense => expense.date === date);
  } catch (error) {
    console.error("Erreur lors de la récupération des dépenses par date:", error);
    return [];
  }
};

// Fonction pour supprimer une dépense
export const deleteExpense = async (expenseId) => {
  try {
    // Simuler la suppression d'une dépense
    console.log(`Suppression de la dépense: ${expenseId}`);
    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la suppression de la dépense:", error);
    return { success: false };
  }
};