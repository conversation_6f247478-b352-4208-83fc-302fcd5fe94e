import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { View, ViewProps } from 'react-native';

type ThemedViewProps = ViewProps & {
  lightColor?: string;
  darkColor?: string;
};

export function ThemedView(props: ThemedViewProps) {
  const { style, lightColor, darkColor, ...otherProps } = props;
  const colorScheme = useColorScheme();
  
  const backgroundColor = colorScheme === 'dark' 
    ? darkColor || Colors.dark.background 
    : lightColor || Colors.light.background;

  console.log('ThemedView rendering with backgroundColor:', backgroundColor);
  
  return <View style={[{ backgroundColor }, style]} {...otherProps} />;
}

