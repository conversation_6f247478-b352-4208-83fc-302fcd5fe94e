import { Colors } from '@/constants/Colors';
import { APP_NAME } from '@/constants/Strings';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function AppHeader({ title, showBackButton = false, showProfileButton = true }) {
  const router = useRouter();
  
  return (
    <View style={styles.header}>
      <View style={styles.leftContainer}>
        {showBackButton && (
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color={Colors.light.tint} />
          </TouchableOpacity>
        )}
        <Text style={styles.headerText}>{title || APP_NAME}</Text>
      </View>
      
      {showProfileButton && (
        <TouchableOpacity 
          style={styles.profileButton}
          onPress={() => router.navigate('profile')}
        >
          <Ionicons name="person-circle-outline" size={30} color={Colors.light.tint} />
        </TouchableOpacity>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'transparent',
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 12,
  },
  headerText: {
    fontSize: 22,
    fontWeight: 'bold',
    color: Colors.light.tint,
  },
  profileButton: {
    padding: 8,
  },
});