import { Colors } from '@/constants/Colors';
import { loginUser, resetPassword, signInWithApple, signInWithFacebook, signInWithGoogle } from '@/firebaseServices';
import { Ionicons } from '@expo/vector-icons';
import * as AppleAuthentication from 'expo-apple-authentication';
import * as Facebook from 'expo-auth-session/providers/facebook';
import * as Google from 'expo-auth-session/providers/google';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, SafeAreaView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { ActivityIndicator, Button, Divider, HelperText, Modal, TextInput, Title } from 'react-native-paper';

// Configuration pour Google OAuth
const googleClientId = '638031423577-rvvjnl9v7hn7kkqtgbhkq9rvjn4f8vqd.apps.googleusercontent.com';
const googleAndroidClientId = '638031423577-rvvjnl9v7hn7kkqtgbhkq9rvjn4f8vqd.apps.googleusercontent.com';
const googleIosClientId = '638031423577-rvvjnl9v7hn7kkqtgbhkq9rvjn4f8vqd.apps.googleusercontent.com';

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showResetModal, setShowResetModal] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [resetEmailError, setResetEmailError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  
  // Configuration des providers d'authentification
  const [googleRequest, googleResponse, promptGoogleAsync] = Google.useAuthRequest({
    expoClientId: googleClientId,
    androidClientId: googleAndroidClientId,
    iosClientId: googleIosClientId,
    scopes: ['profile', 'email']
  });
  
  const [fbRequest, fbResponse, promptFacebookAsync] = Facebook.useAuthRequest({
    clientId: '1294074715735452',
  });
  
  // Gérer la réponse de Google
  useEffect(() => {
    if (googleResponse?.type === 'success') {
      const { id_token } = googleResponse.params;
      handleGoogleSignInWithToken(id_token);
    }
  }, [googleResponse]);
  
  // Gérer la réponse de Facebook
  useEffect(() => {
    if (fbResponse?.type === 'success') {
      const { access_token } = fbResponse.params;
      handleFacebookSignInWithToken(access_token);
    }
  }, [fbResponse]);
  
  // Validation de l'email
  const validateEmail = () => {
    if (!email) {
      setEmailError('L\'email est requis');
      return false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setEmailError('Format d\'email invalide');
      return false;
    } else {
      setEmailError('');
      return true;
    }
  };
  
  // Validation du mot de passe
  const validatePassword = () => {
    if (!password) {
      setPasswordError('Le mot de passe est requis');
      return false;
    } else if (password.length < 6) {
      setPasswordError('Le mot de passe doit contenir au moins 6 caractères');
      return false;
    } else {
      setPasswordError('');
      return true;
    }
  };
  
  // Validation de tous les champs
  const validateInputs = () => {
    const isEmailValid = validateEmail();
    const isPasswordValid = validatePassword();
    return isEmailValid && isPasswordValid;
  };

  const handleLogin = async () => {
    if (!validateInputs()) return;
    
    setLoading(true);
    try {
      const { user, error } = await loginUser(email, password);
      
      if (error) {
        Alert.alert('Erreur de connexion', 'Email ou mot de passe incorrect.');
      } else if (user) {
        router.replace('/(tabs)');
      }
    } catch (error) {
      Alert.alert('Erreur', 'Problème lors de la connexion.');
    } finally {
      setLoading(false);
    }
  };

  // Validation de l'email pour la réinitialisation
  const validateResetEmail = () => {
    if (!resetEmail) {
      setResetEmailError('L\'email est requis');
      return false;
    } else if (!/\S+@\S+\.\S+/.test(resetEmail)) {
      setResetEmailError('Format d\'email invalide');
      return false;
    } else {
      setResetEmailError('');
      return true;
    }
  };

  // Gestion de la réinitialisation du mot de passe
  const handleResetPassword = async () => {
    if (!validateResetEmail()) return;
    
    setLoading(true);
    try {
      const { success, error } = await resetPassword(resetEmail);
      
      if (error) {
        Alert.alert('Erreur', 'Impossible de réinitialiser le mot de passe.');
      } else if (success) {
        Alert.alert('Succès', 'Instructions de réinitialisation envoyées à votre email.');
        setShowResetModal(false);
      }
    } catch (error) {
      Alert.alert('Erreur', 'Problème lors de la réinitialisation du mot de passe.');
    } finally {
      setLoading(false);
    }
  };

  // Fonctions pour les connexions sociales
  const handleGoogleSignIn = async () => {
    try {
      setLoading(true);
      await promptGoogleAsync();
    } catch (error) {
      console.error('Erreur lors de la connexion Google:', error);
      Alert.alert('Erreur', 'Problème lors de la connexion avec Google.');
    } finally {
      setLoading(false);
    }
  };
  
  const handleGoogleSignInWithToken = async (idToken) => {
    try {
      setLoading(true);
      const { user, error } = await signInWithGoogle(idToken);
      
      if (error) {
        Alert.alert('Erreur', 'Problème lors de la connexion avec Google.');
      } else if (user) {
        router.replace('/(tabs)');
      }
    } catch (error) {
      Alert.alert('Erreur', 'Problème lors de la connexion avec Google.');
    } finally {
      setLoading(false);
    }
  };

  const handleFacebookSignIn = async () => {
    try {
      setLoading(true);
      await promptFacebookAsync();
    } catch (error) {
      console.error('Erreur lors de la connexion Facebook:', error);
      Alert.alert('Erreur', 'Problème lors de la connexion avec Facebook.');
    } finally {
      setLoading(false);
    }
  };
  
  const handleFacebookSignInWithToken = async (accessToken) => {
    try {
      setLoading(true);
      const { user, error } = await signInWithFacebook(accessToken);
      
      if (error) {
        Alert.alert('Erreur', 'Problème lors de la connexion avec Facebook.');
      } else if (user) {
        router.replace('/(tabs)');
      }
    } catch (error) {
      Alert.alert('Erreur', 'Problème lors de la connexion avec Facebook.');
    } finally {
      setLoading(false);
    }
  };

  const handleAppleSignIn = async () => {
    try {
      setLoading(true);
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });
      
      // Utiliser le credential pour s'authentifier avec Firebase
      const { user, error } = await signInWithApple(credential.identityToken, credential.nonce);
      
      if (error) {
        Alert.alert('Erreur', 'Problème lors de la connexion avec Apple.');
      } else if (user) {
        router.replace('/(tabs)');
      }
    } catch (error) {
      if (error.code !== 'ERR_CANCELED') {
        Alert.alert('Erreur', 'Problème lors de la connexion avec Apple.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={Colors.light.text} />
        </TouchableOpacity>
      </View>
      
      <Title style={styles.title}>Connexion</Title>
      
      <TextInput
        label="Email"
        value={email}
        onChangeText={setEmail}
        onBlur={validateEmail}
        style={styles.input}
        keyboardType="email-address"
        autoCapitalize="none"
        left={<TextInput.Icon icon="email" />}
        error={!!emailError}
      />
      {emailError ? <HelperText type="error">{emailError}</HelperText> : null}
      
      <TextInput
        label="Mot de passe"
        value={password}
        onChangeText={setPassword}
        onBlur={validatePassword}
        style={styles.input}
        secureTextEntry={!showPassword}
        left={<TextInput.Icon icon="lock" />}
        right={
          <TextInput.Icon 
            icon={showPassword ? "eye-off" : "eye"} 
            onPress={() => setShowPassword(!showPassword)}
          />
        }
        error={!!passwordError}
      />
      {passwordError ? <HelperText type="error">{passwordError}</HelperText> : null}
      
      <TouchableOpacity 
        onPress={() => setShowResetModal(true)}
        style={styles.forgotPasswordContainer}
      >
        <Text style={styles.forgotPasswordText}>Mot de passe oublié ?</Text>
      </TouchableOpacity>
      
      {loading ? (
        <ActivityIndicator size="large" color={Colors.light.tint} style={styles.loader} />
      ) : (
        <Button mode="contained" style={styles.button} onPress={handleLogin}>
          Se connecter
        </Button>
      )}
      
      <Divider style={styles.divider} />
      <Text style={styles.orText}>OU</Text>
      
      <View style={styles.socialButtonsContainer}>
        <TouchableOpacity 
          style={[styles.socialButton, styles.googleButton]}
          onPress={handleGoogleSignIn}
          disabled={loading}
        >
          <Ionicons name="logo-google" size={24} color="#DB4437" />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.socialButton, styles.facebookButton]}
          onPress={handleFacebookSignIn}
          disabled={loading}
        >
          <Ionicons name="logo-facebook" size={24} color="#4267B2" />
        </TouchableOpacity>
        
        {AppleAuthentication.isAvailableAsync() && (
          <TouchableOpacity 
            style={[styles.socialButton, styles.appleButton]}
            onPress={handleAppleSignIn}
            disabled={loading}
          >
            <Ionicons name="logo-apple" size={24} color="#000000" />
          </TouchableOpacity>
        )}
      </View>
      
      <Button
        mode="text"
        style={styles.linkButton}
        onPress={() => router.push('/register')}
      >
        Pas encore de compte ? S'inscrire
      </Button>
      
      {/* Modal de réinitialisation de mot de passe */}
      <Modal
        visible={showResetModal}
        onDismiss={() => setShowResetModal(false)}
        contentContainerStyle={styles.modalContainer}
      >
        <Title style={styles.modalTitle}>Réinitialiser le mot de passe</Title>
        <Text style={styles.modalText}>
          Entrez votre adresse email pour recevoir un lien de réinitialisation.
        </Text>
        
        <TextInput
          label="Email"
          value={resetEmail}
          onChangeText={setResetEmail}
          onBlur={validateResetEmail}
          style={styles.input}
          keyboardType="email-address"
          autoCapitalize="none"
          left={<TextInput.Icon icon="email" />}
          error={!!resetEmailError}
        />
        {resetEmailError ? <HelperText type="error">{resetEmailError}</HelperText> : null}
        
        <View style={styles.modalButtonsContainer}>
          <Button 
            mode="outlined" 
            onPress={() => setShowResetModal(false)}
            style={styles.modalButton}
          >
            Annuler
          </Button>
          
          <Button 
            mode="contained" 
            onPress={handleResetPassword}
            style={styles.modalButton}
            loading={loading}
            disabled={loading}
          >
            Envoyer
          </Button>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 30,
    textAlign: 'center',
  },
  input: {
    marginBottom: 10,
  },
  forgotPasswordContainer: {
    alignSelf: 'flex-end',
    marginBottom: 20,
  },
  forgotPasswordText: {
    color: Colors.light.tint,
  },
  button: {
    marginTop: 10,
    paddingVertical: 8,
  },
  loader: {
    marginTop: 20,
  },
  divider: {
    marginVertical: 30,
  },
  orText: {
    textAlign: 'center',
    marginTop: -40,
    backgroundColor: '#fff',
    paddingHorizontal: 10,
    alignSelf: 'center',
    color: '#888',
  },
  socialButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 30,
  },
  socialButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  googleButton: {
    backgroundColor: '#fff',
  },
  facebookButton: {
    backgroundColor: '#fff',
  },
  appleButton: {
    backgroundColor: '#fff',
  },
  linkButton: {
    marginTop: 10,
  },
  modalContainer: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 10,
  },
  modalTitle: {
    textAlign: 'center',
    marginBottom: 10,
  },
  modalText: {
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
  },
  modalButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 5,
  },
});
