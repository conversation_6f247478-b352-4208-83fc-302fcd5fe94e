// firebase-config.js - <PERSON><PERSON>er unique pour Firebase
import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

// Configuration Firebase
const firebaseConfig = {
  apiKey: "AIzaSyDZYVxe-GZ1xWLl2UYVu_hmHCjZM9lNxTs",
  authDomain: "mboabudget.firebaseapp.com",
  projectId: "mboabudget",
  storageBucket: "mboabudget.appspot.com",
  messagingSenderId: "1045282978405",
  appId: "1:1045282978405:web:c9e9f1a7a4d8f3a5a5a5a5"
};

// Initialiser Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// Fonction pour obtenir l'utilisateur actuel
const getCurrentUser = () => {
  return auth.currentUser;
};

// Fonctions d'authentification
const loginUser = async (email, password) => {
  try {
    // Simuler une connexion réussie pour le moment
    return { user: { email }, error: null };
  } catch (error) {
    return { user: null, error };
  }
};

const registerUser = async (email, password) => {
  try {
    // Simuler une inscription réussie pour le moment
    return { user: { email }, error: null };
  } catch (error) {
    return { user: null, error };
  }
};

const resetPassword = async (email) => {
  try {
    // Simuler une réinitialisation réussie pour le moment
    return { success: true, error: null };
  } catch (error) {
    return { success: false, error };
  }
};

const signInWithGoogle = async (idToken) => {
  try {
    // Simuler une connexion Google réussie pour le moment
    return { user: { email: "<EMAIL>" }, error: null };
  } catch (error) {
    return { user: null, error };
  }
};

const signInWithFacebook = async (accessToken) => {
  try {
    // Simuler une connexion Facebook réussie pour le moment
    return { user: { email: "<EMAIL>" }, error: null };
  } catch (error) {
    return { user: null, error };
  }
};

const signInWithApple = async (identityToken, nonce) => {
  try {
    // Simuler une connexion Apple réussie pour le moment
    return { user: { email: "<EMAIL>" }, error: null };
  } catch (error) {
    return { user: null, error };
  }
};

export {
  app,
  auth,
  db,
  getCurrentUser,
  loginUser,
  registerUser,
  resetPassword, signInWithApple, signInWithFacebook, signInWithGoogle
};
