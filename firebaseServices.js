// firebaseServices.js - Services Firebase
import { initializeFirebase } from './firebaseSetup';

// Initialiser Firebase et récupérer les instances
const { app, auth, db } = initializeFirebase();

// Fonctions d'authentification
const loginUser = async (email, password) => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return { user: null, error: new Error("Service d'authentification non disponible") };
    }
    
    // Simuler une connexion réussie pour le moment
    return { user: { email }, error: null };
  } catch (error) {
    console.error('Erreur lors de la connexion:', error);
    return { user: null, error };
  }
};

const registerUser = async (email, password) => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return { user: null, error: new Error("Service d'authentification non disponible") };
    }
    
    // Simuler une inscription réussie pour le moment
    return { user: { email }, error: null };
  } catch (error) {
    console.error('Erreur lors de l\'inscription:', error);
    return { user: null, error };
  }
};

const resetPassword = async (email) => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return { success: false, error: new Error("Service d'authentification non disponible") };
    }
    
    // Simuler une réinitialisation réussie pour le moment
    return { success: true, error: null };
  } catch (error) {
    console.error("Erreur de réinitialisation:", error);
    return { success: false, error };
  }
};

const signInWithGoogle = async (idToken) => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return { user: null, error: new Error("Service d'authentification non disponible") };
    }
    
    // Simuler une connexion Google réussie pour le moment
    return { user: { email: "<EMAIL>" }, error: null };
  } catch (error) {
    console.error('Erreur lors de la connexion avec Google:', error);
    return { user: null, error };
  }
};

const signInWithFacebook = async (accessToken) => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return { user: null, error: new Error("Service d'authentification non disponible") };
    }
    
    // Simuler une connexion Facebook réussie pour le moment
    return { user: { email: "<EMAIL>" }, error: null };
  } catch (error) {
    console.error('Erreur lors de la connexion avec Facebook:', error);
    return { user: null, error };
  }
};

const signInWithApple = async (identityToken, nonce) => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return { user: null, error: new Error("Service d'authentification non disponible") };
    }
    
    // Simuler une connexion Apple réussie pour le moment
    return { user: { email: "<EMAIL>" }, error: null };
  } catch (error) {
    console.error('Erreur lors de la connexion avec Apple:', error);
    return { user: null, error };
  }
};

// Fonction pour obtenir l'utilisateur actuel
const getCurrentUser = () => {
  if (auth) {
    return auth.currentUser;
  }
  return null;
};

// Créer un objet pour les services de fallback
const fallbackServices = {
  getCategoryBudgets: async () => [],
  getGlobalBudget: async () => 0,
  calculateTotalSpent: async () => 0,
  updateCategoryBudget: async () => ({ success: false }),
  transferBudgetBetweenCategories: async () => ({ success: false }),
  calculateTotalBudget: async () => 0,
  resetAllBudgets: async () => false,
  saveGlobalBudget: async () => false,
  saveCategoryBudgets: async () => false,
  getBudgetForCategory: async (category) => ({ category, amount: 0, spent: 0 }),
  addExpense: async () => ({ success: false }),
  getUserExpenses: async () => [],
  deleteExpense: async () => ({ success: false }),
  getExpensesByDate: async () => []
};

// Exporter les services
export {
  app,
  auth,
  db, fallbackServices, getCurrentUser,
  loginUser,
  registerUser,
  resetPassword, signInWithApple, signInWithFacebook, signInWithGoogle
};
