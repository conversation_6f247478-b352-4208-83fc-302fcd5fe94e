{"expo": {"name": "projet_tut", "slug": "projet_tut", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "projettut", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "Cette application a besoin d'accéder à votre appareil photo pour prendre une photo de profil.", "NSPhotoLibraryUsageDescription": "Cette application a besoin d'accéder à vos photos pour mettre à jour votre avatar.", "SKAdNetworkItems": [{"SKAdNetworkIdentifier": "v9wttpbfk9.skadnetwork"}, {"SKAdNetworkIdentifier": "n38lu8286q.skadnetwork"}], "ITSAppUsesNonExemptEncryption": false, "CFBundleURLTypes": [{"CFBundleURLSchemes": ["projettut", "fb1294074715735452"]}], "LSApplicationQueriesSchemes": ["fbapi", "fb-messenger-api", "fbauth2", "fbshareextension"]}, "bundleIdentifier": "com.anonymous.projet-tut"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.INTERNET", "android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"], "package": "com.anonymous.projet_tut"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["react-native-fbsdk-next", {"appID": "1294074715735452", "clientToken": "b5726dd32c1e114e02da3260e99a9db6", "displayName": "MoneyTracker"}], "expo-web-browser", ["expo-image-picker", {"photosPermission": "L'application a besoin d'accéder à vos photos pour mettre à jour votre avatar.", "cameraPermission": "L'application a besoin d'accéder à votre appareil photo pour prendre une photo de profil."}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "4621d9ce-e723-492b-b003-f2da7cee907b"}}}}