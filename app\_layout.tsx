import { theme } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Slot, SplashScreen } from 'expo-router';
import { useEffect, useState } from 'react';
import { StyleSheet } from 'react-native';
import { Provider as PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';

// Empêcher l'écran de démarrage de se cacher automatiquement
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [appIsReady, setAppIsReady] = useState(false);
  const colorScheme = useColorScheme();

  useEffect(() => {
    // Initialiser l'application
    async function prepareApp() {
      try {
        console.log("App initialization started in _layout.tsx");
        
        // Attendre un peu pour simuler le chargement
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error("App initialization error in _layout.tsx:", error);
      } finally {
        // Marquer l'application comme prête
        setAppIsReady(true);
        // Masquer l'écran de démarrage
        await SplashScreen.hideAsync();
      }
    }

    prepareApp();
  }, []);

  if (!appIsReady) {
    return null;
  }

  return (
    <SafeAreaProvider style={styles.container}>
      <PaperProvider theme={theme}>
        <Slot />
      </PaperProvider>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

