import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { APP_NAME } from '@/constants/Strings';
import { getCurrentUser } from '@/firebaseConfig';
import { Expense, getExpensesByDate, getUserExpenses } from '@/services/expenseService';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { Calendar } from 'react-native-calendars';
import { Card, Chip, Divider, Title } from 'react-native-paper';

// Assurez-vous que cette ligne est présente au début de la fonction principale
export default function CalendarScreen() {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0]); // YYYY-MM-DD
  const [selectedDayExpenses, setSelectedDayExpenses] = useState<Expense[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [monthlyTotal, setMonthlyTotal] = useState<number>(0);
  const [user, setUser] = useState<any>(null);

  // Charger l'utilisateur au démarrage
  useEffect(() => {
    loadUser();
  }, []);

  // Charger toutes les dépenses au démarrage
  useEffect(() => {
    loadExpenses();
  }, []);

  // Charger les dépenses pour la date sélectionnée
  useEffect(() => {
    loadExpensesByDate(selectedDate);
  }, [selectedDate, expenses]);

  // Calculer le total mensuel quand les dépenses changent
  useEffect(() => {
    calculateMonthlyTotal();
  }, [expenses, selectedDate]);

  const loadUser = async () => {
    try {
      const currentUser = await getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('Erreur lors du chargement de l\'utilisateur:', error);
    }
  };

  const loadExpenses = async () => {
    setLoading(true);
    try {
      const userExpenses = await getUserExpenses();
      setExpenses(userExpenses);
    } catch (error) {
      Alert.alert('Erreur', 'Impossible de charger les dépenses.');
    } finally {
      setLoading(false);
    }
  };

  const loadExpensesByDate = async (date: string) => {
    try {
      const dayExpenses = await getExpensesByDate(date);
      setSelectedDayExpenses(dayExpenses);
    } catch (error) {
      console.error('Erreur lors du chargement des dépenses par date:', error);
    }
  };

  // Calculer le total des dépenses pour le mois en cours
  const calculateMonthlyTotal = () => {
    if (!expenses.length) return;
    
    const currentMonth = selectedDate.substring(0, 7); // YYYY-MM
    const monthExpenses = expenses.filter(expense => expense.date.startsWith(currentMonth));
    const total = monthExpenses.reduce((sum, expense) => sum + expense.amount, 0);
    setMonthlyTotal(total);
  };

  // Marquer les dates avec des dépenses et ajouter des points de couleur selon le montant
  const markedDates = expenses.reduce((acc, expense) => {
    // Si la date existe déjà, mettre à jour le montant total
    if (acc[expense.date]) {
      acc[expense.date].total = (acc[expense.date].total || 0) + expense.amount;
    } else {
      // Sinon, créer une nouvelle entrée
      acc[expense.date] = { 
        marked: true, 
        dotColor: Colors.light.tint,
        total: expense.amount
      };
    }
    
    // Déterminer la couleur du point en fonction du montant
    const total = acc[expense.date].total;
    if (total > 10000) {
      acc[expense.date].dotColor = 'red';
    } else if (total > 5000) {
      acc[expense.date].dotColor = 'orange';
    } else {
      acc[expense.date].dotColor = Colors.light.tint;
    }
    
    return acc;
  }, {} as { [key: string]: any });

  const renderExpense = ({ item }: { item: Expense }) => (
    <Card style={styles.expenseCard}>
      <Card.Content>
        <View style={styles.expenseHeader}>
          <Chip icon="tag" style={styles.categoryChip}>{item.category}</Chip>
          <Text style={styles.expenseAmount}>{item.amount} FCFA</Text>
        </View>
        <Divider style={styles.divider} />
        <Text style={styles.expenseDate}>
          <Ionicons name="calendar-outline" size={16} color={Colors.light.text} /> {item.date.split('-').reverse().join('/')}
        </Text>
      </Card.Content>
    </Card>
  );

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.light.tint} />
        <Text style={styles.loadingText}>Chargement des dépenses...</Text>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.header}>
            <View style={styles.logoContainer}>
              <Text style={styles.logoText}>{APP_NAME}</Text>
            </View>
            <TouchableOpacity 
              style={styles.loginButton}
              onPress={() => router.push('/login')}
            >
              <Ionicons name={user ? "person" : "person-outline"} size={24} color={Colors.light.tint} />
            </TouchableOpacity>
          </View>
          
          {/* Résumé mensuel */}
          <Card style={styles.monthSummaryCard}>
            <Card.Content>
              <Text style={styles.monthSummaryTitle}>
                Total du mois: {monthlyTotal} FCFA
              </Text>
              <Text style={styles.monthSummarySubtitle}>
                {selectedDate.substring(0, 7).split('-').reverse().join('/')}
              </Text>
            </Card.Content>
          </Card>
          
          <Calendar
            onDayPress={(day) => setSelectedDate(day.dateString)}
            markedDates={{
              ...markedDates,
              [selectedDate]: { 
                selected: true, 
                selectedColor: Colors.light.tint,
                marked: markedDates[selectedDate]?.marked || false,
                dotColor: markedDates[selectedDate]?.dotColor || Colors.light.tint
              },
            }}
            theme={{
              selectedDayBackgroundColor: Colors.light.tint,
              todayTextColor: Colors.light.tint,
              arrowColor: Colors.light.tint,
              monthTextColor: Colors.light.tint,
              textMonthFontWeight: 'bold',
              textMonthFontSize: 16,
            }}
          />
          
          <View style={styles.expensesContainer}>
            <Title style={styles.subtitle}>
              Dépenses du {selectedDate.split('-').reverse().join('/')}
            </Title>
            
            {selectedDayExpenses.length > 0 ? (
              <FlatList
                data={selectedDayExpenses}
                renderItem={renderExpense}
                keyExtractor={(item) => item.id}
                style={styles.expenseList}
                scrollEnabled={false}
              />
            ) : (
              <Text style={styles.noData}>Aucune dépense pour cette date.</Text>
            )}
          </View>
        </ScrollView>
      </SafeAreaView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
    paddingVertical: 5,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.tint,
    marginLeft: 5,
  },
  loginButton: {
    padding: 5,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  monthSummaryCard: {
    marginBottom: 15,
    backgroundColor: Colors.light.tint,
  },
  monthSummaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  monthSummarySubtitle: {
    fontSize: 14,
    color: 'white',
    opacity: 0.8,
  },
  expensesContainer: {
    marginTop: 20,
    flex: 1,
  },
  expenseList: {
    marginTop: 10,
  },
  expenseCard: {
    marginBottom: 10,
    elevation: 2,
  },
  expenseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryChip: {
    backgroundColor: Colors.light.background,
  },
  expenseAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  expenseDate: {
    marginTop: 8,
    fontSize: 14,
    color: 'gray',
  },
  divider: {
    marginVertical: 8,
  },
  noData: {
    textAlign: 'center',
    marginTop: 20,
    fontSize: 16,
    color: 'gray',
  },
  legendContainer: {
    marginTop: 20,
    padding: 15,
    backgroundColor: Colors.light.background,
    borderRadius: 10,
    marginBottom: 20,
  },
  legendTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendText: {
    fontSize: 14,
  },
});
