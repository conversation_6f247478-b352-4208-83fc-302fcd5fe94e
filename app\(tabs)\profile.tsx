import { getCurrentUser, logoutUser, updateUserProfile } from '@/services/authService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as ImagePicker from 'expo-image-picker';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Button, TextInput } from 'react-native-paper';

export default function ProfileScreen() {
  const [user, setUser] = useState<any>(null);
  const [displayName, setDisplayName] = useState('');
  const [photoURL, setPhotoURL] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadUser();
  }, []);

  const loadUser = async () => {
    try {
      const currentUser = await getCurrentUser();
      setUser(currentUser);
      if (currentUser) {
        setDisplayName(currentUser.displayName || '');
        setPhotoURL(currentUser.photoURL || null);
      }
    } catch (error) {
      console.error('Erreur lors du chargement de l\'utilisateur:', error);
    }
  };

  const handleLogout = async () => {
    try {
      await logoutUser();
      setUser(null);
      router.replace('/login');
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
      Alert.alert('Erreur', 'Impossible de se déconnecter');
    }
  };

  const saveProfile = async () => {
    setLoading(true);
    try {
      const { user: updatedUser, error } = await updateUserProfile(displayName, photoURL || undefined);
      
      if (error) {
        Alert.alert('Erreur', 'Impossible de mettre à jour le profil');
      } else if (updatedUser) {
        setUser(updatedUser);
        Alert.alert('Succès', 'Profil mis à jour avec succès');
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour du profil:', error);
      Alert.alert('Erreur', 'Impossible de mettre à jour le profil');
    } finally {
      setLoading(false);
    }
  };

  const pickImage = async () => {
    try {
      // Demander les permissions
      const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
      const { status: libraryStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (cameraStatus !== 'granted' || libraryStatus !== 'granted') {
        Alert.alert('Permission requise', 'Nous avons besoin de votre permission pour accéder à la caméra et à la galerie.');
        return;
      }

      // Afficher les options
      Alert.alert(
        "Choisir une image",
        "Sélectionnez une source pour votre avatar",
        [
          {
            text: "Appareil photo",
            onPress: async () => {
              try {
                const result = await ImagePicker.launchCameraAsync({
                  mediaTypes: ImagePicker.MediaTypeOptions.Images,
                  allowsEditing: true,
                  aspect: [1, 1],
                  quality: 0.5,
                });
                
                if (!result.canceled) {
                  setPhotoURL(result.assets[0].uri);
                }
              } catch (error) {
                console.error("Erreur lors de la prise de photo:", error);
                Alert.alert("Erreur", "Impossible de prendre une photo");
              }
            }
          },
          {
            text: "Galerie",
            onPress: async () => {
              try {
                const result = await ImagePicker.launchImageLibraryAsync({
                  mediaTypes: ImagePicker.MediaTypeOptions.Images,
                  allowsEditing: true,
                  aspect: [1, 1],
                  quality: 0.5,
                });
                
                if (!result.canceled) {
                  setPhotoURL(result.assets[0].uri);
                }
              } catch (error) {
                console.error("Erreur lors de la sélection d'image:", error);
                Alert.alert("Erreur", "Impossible de sélectionner une image");
              }
            }
          },
          {
            text: "Annuler",
            style: "cancel"
          }
        ]
      );
    } catch (error) {
      console.error("Erreur lors de l'accès à la caméra/galerie:", error);
      Alert.alert("Erreur", "Impossible d'accéder à la caméra ou à la galerie");
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#0a7ea4" />
        <Text style={styles.loadingText}>Chargement...</Text>
      </View>
    );
  }

  if (!user) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Vous n'êtes pas connecté</Text>
        <TouchableOpacity 
          style={styles.button}
          onPress={() => router.push('/login')}
        >
          <Text style={styles.buttonText}>Se connecter</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={styles.scrollView}>
      <View style={styles.container}>
        <Text style={styles.title}>Mon Profil</Text>
        <View style={styles.userInfo}>
          <View style={styles.avatar}>
            {profileImage ? (
              <Image source={{ uri: profileImage }} style={styles.avatarImage} />
            ) : (
              <Text style={styles.avatarText}>
                {displayName ? displayName.substring(0, 2).toUpperCase() : user.email.substring(0, 2).toUpperCase()}
              </Text>
            )}
          </View>
          <TouchableOpacity onPress={pickImage} style={styles.editAvatarButton}>
            <Text style={styles.editAvatarButtonText}>Modifier l'avatar</Text>
          </TouchableOpacity>
          <Text style={styles.displayName}>{displayName || 'Utilisateur'}</Text>
          <Text style={styles.email}>{user.email}</Text>
        </View>

        <View style={styles.infoCard}>
          <View style={styles.infoRow}>
            <Ionicons name="call-outline" size={20} color="#0a7ea4" />
            <Text style={styles.infoLabel}>Téléphone:</Text>
            <Text style={styles.infoValue}>{phoneNumber || 'Non renseigné'}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="calendar-outline" size={20} color="#0a7ea4" />
            <Text style={styles.infoLabel}>Date de naissance:</Text>
            <Text style={styles.infoValue}>{dateOfBirth || 'Non renseignée'}</Text>
          </View>
          
          {gender && (
            <View style={styles.infoRow}>
              <Ionicons name="person-outline" size={20} color="#0a7ea4" />
              <Text style={styles.infoLabel}>Genre:</Text>
              <Text style={styles.infoValue}>{gender}</Text>
            </View>
          )}
          
          {address && (
            <View style={styles.infoRow}>
              <Ionicons name="location-outline" size={20} color="#0a7ea4" />
              <Text style={styles.infoLabel}>Adresse:</Text>
              <Text style={styles.infoValue}>{address}</Text>
            </View>
          )}
          
          {occupation && (
            <View style={styles.infoRow}>
              <Ionicons name="briefcase-outline" size={20} color="#0a7ea4" />
              <Text style={styles.infoLabel}>Profession:</Text>
              <Text style={styles.infoValue}>{occupation}</Text>
            </View>
          )}
          
          {bio && (
            <View style={styles.bioContainer}>
              <Text style={styles.bioLabel}>À propos de moi:</Text>
              <Text style={styles.bioText}>{bio}</Text>
            </View>
          )}
        </View>
        
        <TouchableOpacity 
          style={styles.button}
          onPress={() => setEditModalVisible(true)}
        >
          <Text style={styles.buttonText}>Modifier le profil</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.logoutButton]}
          onPress={handleLogout}
        >
          <Text style={styles.buttonText}>Se déconnecter</Text>
        </TouchableOpacity>
        
        <Modal
          visible={editModalVisible}
          animationType="slide"
          onRequestClose={() => setEditModalVisible(false)}
        >
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Modifier le profil</Text>
            <ScrollView style={styles.modalScrollView}>
              <TextInput
                label="Nom d'affichage"
                value={displayName}
                onChangeText={setDisplayName}
                style={styles.input}
              />
              <TextInput
                label="Numéro de téléphone"
                value={phoneNumber}
                onChangeText={setPhoneNumber}
                style={styles.input}
                keyboardType="phone-pad"
              />
              <TextInput
                label="Date de naissance (JJ/MM/AAAA)"
                value={dateOfBirth}
                onChangeText={setDateOfBirth}
                style={styles.input}
                placeholder="JJ/MM/AAAA"
              />
              <TextInput
                label="Genre"
                value={gender}
                onChangeText={setGender}
                style={styles.input}
                placeholder="Homme, Femme, Autre"
              />
              <TextInput
                label="Adresse"
                value={address}
                onChangeText={setAddress}
                style={styles.input}
                placeholder="Votre adresse"
              />
              <TextInput
                label="Profession"
                value={occupation}
                onChangeText={setOccupation}
                style={styles.input}
                placeholder="Votre profession"
              />
              <TextInput
                label="Bio"
                value={bio}
                onChangeText={setBio}
                style={styles.input}
                multiline
                numberOfLines={4}
                placeholder="Parlez-nous de vous..."
              />
            </ScrollView>
            <Button
              mode="contained"
              onPress={saveUserProfile}
              style={styles.saveButton}
              loading={uploading}
            >
              {uploading ? 'Sauvegarde...' : 'Sauvegarder'}
            </Button>
            <Button
              mode="text"
              onPress={() => setEditModalVisible(false)}
              style={styles.cancelButton}
            >
              Annuler
            </Button>
          </View>
        </Modal>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    padding: 20,
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
    color: '#333',
  },
  userInfo: {
    alignItems: 'center',
    marginBottom: 30,
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 10,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#0a7ea4',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  avatarText: {
    color: '#ffffff',
    fontSize: 36,
    fontWeight: 'bold',
  },
  avatarImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  displayName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  email: {
    fontSize: 16,
    color: '#666',
  },
  infoCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 10,
    width: '100%',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  infoLabel: {
    fontSize: 16,
    color: '#666',
    marginLeft: 10,
    width: 120,
  },
  infoValue: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  bioContainer: {
    marginTop: 10,
  },
  bioLabel: {
    fontSize: 16,
    color: '#666',
    marginBottom: 5,
  },
  bioText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 22,
  },
  button: {
    backgroundColor: '#0a7ea4',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 5,
    width: '100%',
    alignItems: 'center',
    marginBottom: 15,
  },
  logoutButton: {
    backgroundColor: '#e74c3c',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#333',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
    padding: 20,
  },
  modalScrollView: {
    flex: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#333',
    textAlign: 'center',
  },
  input: {
    marginBottom: 15,
    backgroundColor: '#f9f9f9',
  },
  saveButton: {
    marginBottom: 10,
    backgroundColor: '#0a7ea4',
  },
  cancelButton: {
    marginBottom: 10,
  },
  editAvatarButton: {
    backgroundColor: '#0a7ea4',
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 5,
    marginBottom: 15,
  },
  editAvatarButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
  },
});

const saveProfile = async () => {
  setLoading(true);
  try {
    // Mettre à jour le profil dans AsyncStorage
    const currentUser = await getCurrentUser();
    if (currentUser) {
      const updatedUser = {
        ...currentUser,
        displayName: displayName || currentUser.displayName || '',
        email: currentUser.email
      };
      
      await AsyncStorage.setItem('user', JSON.stringify(updatedUser));
      
      // Essayer de mettre à jour Firebase si disponible
      if (auth && auth.currentUser) {
        try {
          await updateProfile(auth.currentUser, {
            displayName: displayName
          });
          
          // Mettre à jour également dans Firestore
          if (db) {
            await setDoc(doc(db, 'users', auth.currentUser.uid), {
              displayName: displayName,
              updatedAt: new Date()
            }, { merge: true });
          }
        } catch (firebaseError) {
          console.error("Erreur Firebase lors de la mise à jour du profil:", firebaseError);
          // Continuer car nous avons déjà mis à jour AsyncStorage
        }
      }
      
      Alert.alert('Succès', 'Profil mis à jour avec succès');
      setUser(updatedUser);
    }
  } catch (error) {
    console.error('Erreur lors de la mise à jour du profil:', error);
    Alert.alert('Erreur', 'Impossible de mettre à jour le profil');
  } finally {
    setLoading(false);
  }
};

