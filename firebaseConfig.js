// firebaseConfig.js
import AsyncStorage from '@react-native-async-storage/async-storage';
import { initializeApp } from 'firebase/app';
import { getReactNativePersistence, initializeAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

// Configuration Firebase
const firebaseConfig = {
  apiKey: "AIzaSyDZYVxe-GZ1xWLl2UYVu_hmHCjZM9lNxTs",
  authDomain: "mboabudget.firebaseapp.com",
  projectId: "mboabudget",
  storageBucket: "mboabudget.appspot.com",
  messagingSenderId: "1045282978405",
  appId: "1:1045282978405:web:c9e9f1a7a4d8f3a5a5a5a5"
};

// Initialiser Firebase
let app, auth, db;

try {
  console.log("Initializing new Firebase app");
  // Initialiser l'application Firebase
  app = initializeApp(firebaseConfig);
  
  // Initialiser Firestore d'abord
  db = getFirestore(app);
  
  // Puis initialiser Auth avec AsyncStorage persistence
  console.log("Initializing new auth instance with AsyncStorage");
  auth = initializeAuth(app, {
    persistence: getReactNativePersistence(AsyncStorage)
  });
  
  console.log("Firebase initialized successfully in firebaseConfig.js");
} catch (error) {
  console.error("Firebase initialization error in firebaseConfig.js:", error);
}

// Créer un objet pour les services de fallback
const fallbackServices = {
  getCategoryBudgets: async () => [],
  getGlobalBudget: async () => 0,
  calculateTotalSpent: async () => 0,
  updateCategoryBudget: async () => ({ success: false }),
  transferBudgetBetweenCategories: async () => ({ success: false }),
  calculateTotalBudget: async () => 0,
  resetAllBudgets: async () => false,
  saveGlobalBudget: async () => false,
  saveCategoryBudgets: async () => false,
  getBudgetForCategory: async (category) => ({ category, amount: 0, spent: 0 }),
  addExpense: async () => ({ success: false }),
  getUserExpenses: async () => [],
  deleteExpense: async () => ({ success: false }),
  getExpensesByDate: async () => []
};

// Fonction pour obtenir l'utilisateur actuel
const getCurrentUser = () => {
  if (auth) {
    return auth.currentUser;
  }
  return null;
};

export { app, auth, db, fallbackServices, getCurrentUser };

