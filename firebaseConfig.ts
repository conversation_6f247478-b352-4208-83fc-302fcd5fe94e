// Import les instances Firebase déjà initialisées
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as WebBrowser from 'expo-web-browser';
import { initializeApp } from 'firebase/app';
import {
    FacebookAuthProvider,
    GoogleAuthProvider,
    OAuthProvider,
    createUserWithEmailAndPassword,
    getAuth,
    sendPasswordResetEmail,
    signInWithCredential,
    signInWithEmailAndPassword,
    signOut
} from 'firebase/auth';
import { doc, getFirestore, setDoc } from 'firebase/firestore';

// Configuration Firebase
const firebaseConfig = {
  apiKey: "AIzaSyDps0IIn4TZMhuMZlaIqQzQckY39T5LKdk",
  authDomain: "projet-tut-44906.firebaseapp.com",
  projectId: "projet-tut-44906",
  storageBucket: "projet-tut-44906.firebasestorage.app",
  messagingSenderId: "638031423577",
  appId: "1:638031423577:web:917d39664713ed66dfe948",
  measurementId: "G-NWWJ0N2ZBS"
};

// Initialiser l'application Firebase
const app = initializeApp(firebaseConfig);

// Initialiser Auth
const auth = getAuth(app);

// Initialiser Firestore
const db = getFirestore(app);

// Initialiser les providers
const googleProvider = new GoogleAuthProvider();
const facebookProvider = new FacebookAuthProvider();
const appleProvider = new OAuthProvider('apple.com');

WebBrowser.maybeCompleteAuthSession();

// Fonction pour enregistrer un nouvel utilisateur
export const registerUser = async (email: string, password: string) => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return { user: null, error: new Error("Service d'authentification non disponible") };
    }
    
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    // Stocker l'utilisateur dans AsyncStorage
    await AsyncStorage.setItem('user', JSON.stringify({
      uid: user.uid,
      email: user.email,
      displayName: user.displayName
    }));
    
    // Créer un document utilisateur dans Firestore
    if (db) {
      await setDoc(doc(db, 'users', user.uid), {
        email: user.email,
        displayName: user.displayName || '',
        createdAt: new Date()
      });
    }
    
    return { user, error: null };
  } catch (error: any) {
    console.error('Erreur lors de l\'inscription:', error);
    return { user: null, error };
  }
};

// Fonction pour connecter un utilisateur
export const loginUser = async (email: string, password: string) => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return { user: null, error: new Error("Service d'authentification non disponible") };
    }
    
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    // Stocker l'utilisateur dans AsyncStorage
    await AsyncStorage.setItem('user', JSON.stringify({
      uid: user.uid,
      email: user.email,
      displayName: user.displayName
    }));
    
    return { user, error: null };
  } catch (error: any) {
    console.error('Erreur lors de la connexion:', error);
    return { user: null, error };
  }
};

// Fonction pour déconnecter un utilisateur
export const logoutUser = async () => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      await AsyncStorage.removeItem('user');
      return { error: null };
    }
    
    await signOut(auth);
    await AsyncStorage.removeItem('user');
    return { error: null };
  } catch (error: any) {
    console.error('Erreur lors de la déconnexion:', error);
    return { error };
  }
};

// Fonction pour récupérer l'utilisateur actuel
export const getCurrentUser = async () => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return null;
    }
    
    return auth.currentUser;
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'utilisateur:', error);
    return null;
  }
};

// Fonction pour réinitialiser le mot de passe
export const resetPassword = async (email) => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return { success: false, error: new Error("Service d'authentification non disponible") };
    }
    
    await sendPasswordResetEmail(auth, email);
    return { success: true, error: null };
  } catch (error) {
    console.error("Erreur de réinitialisation:", error);
    return { success: false, error };
  }
};

// Fonction pour se connecter avec Google
export const signInWithGoogle = async (idToken) => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return { user: null, error: new Error("Service d'authentification non disponible") };
    }
    
    const credential = GoogleAuthProvider.credential(idToken);
    const userCredential = await signInWithCredential(auth, credential);
    const user = userCredential.user;
    
    // Stocker l'utilisateur dans AsyncStorage
    await AsyncStorage.setItem('user', JSON.stringify({
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL
    }));
    
    // Créer ou mettre à jour le document utilisateur dans Firestore
    if (db) {
      await setDoc(doc(db, 'users', user.uid), {
        email: user.email,
        displayName: user.displayName || '',
        photoURL: user.photoURL || '',
        lastLogin: new Date()
      }, { merge: true });
    }
    
    return { user, error: null };
  } catch (error) {
    console.error('Erreur lors de la connexion avec Google:', error);
    return { user: null, error };
  }
};

// Fonction pour se connecter avec Facebook
export const signInWithFacebook = async (accessToken) => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return { user: null, error: new Error("Service d'authentification non disponible") };
    }
    
    const credential = FacebookAuthProvider.credential(accessToken);
    const userCredential = await signInWithCredential(auth, credential);
    const user = userCredential.user;
    
    // Stocker l'utilisateur dans AsyncStorage
    await AsyncStorage.setItem('user', JSON.stringify({
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL
    }));
    
    // Créer ou mettre à jour le document utilisateur dans Firestore
    if (db) {
      await setDoc(doc(db, 'users', user.uid), {
        email: user.email,
        displayName: user.displayName || '',
        photoURL: user.photoURL || '',
        lastLogin: new Date()
      }, { merge: true });
    }
    
    return { user, error: null };
  } catch (error) {
    console.error('Erreur lors de la connexion avec Facebook:', error);
    return { user: null, error };
  }
};

// Fonction pour se connecter avec Apple
export const signInWithApple = async (idToken, nonce) => {
  try {
    if (!auth) {
      console.error("Auth n'est pas initialisé");
      return { user: null, error: new Error("Service d'authentification non disponible") };
    }
    
    const credential = OAuthProvider.credentialFromJSON({
      providerId: 'apple.com',
      idToken: idToken,
      rawNonce: nonce
    });
    const userCredential = await signInWithCredential(auth, credential);
    const user = userCredential.user;
    
    // Stocker l'utilisateur dans AsyncStorage
    await AsyncStorage.setItem('user', JSON.stringify({
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL
    }));
    
    // Créer ou mettre à jour le document utilisateur dans Firestore
    if (db) {
      await setDoc(doc(db, 'users', user.uid), {
        email: user.email,
        displayName: user.displayName || '',
        photoURL: user.photoURL || '',
        lastLogin: new Date()
      }, { merge: true });
    }
    
    return { user, error: null };
  } catch (error) {
    console.error('Erreur lors de la connexion avec Apple:', error);
    return { user: null, error };
  }
};

// Fonction pour créer les services de secours
function createFallbackServices() {
  return {
    // Budget services
    getCategoryBudgets: async (): Promise<any[]> => {
      try {
        const budgetsJson = await AsyncStorage.getItem('budgets');
        return budgetsJson ? JSON.parse(budgetsJson) : [];
      } catch (error) {
        console.error('Erreur lors de la récupération des budgets locaux:', error);
        return [];
      }
    },

    getGlobalBudget: async (): Promise<number> => {
      try {
        const globalBudgetJson = await AsyncStorage.getItem('globalBudget');
        return globalBudgetJson ? parseFloat(globalBudgetJson) : 0;
      } catch (error) {
        console.error('Erreur lors de la récupération du budget global local:', error);
        return 0;
      }
    },

    saveGlobalBudget: async (amount: string): Promise<boolean> => {
      try {
        await AsyncStorage.setItem('globalBudget', amount);
        return true;
      } catch (error) {
        console.error('Erreur lors de la sauvegarde du budget global local:', error);
        return false;
      }
    },

    saveCategoryBudgets: async (budgets: any[]): Promise<boolean> => {
      try {
        await AsyncStorage.setItem('budgets', JSON.stringify(budgets));
        return true;
      } catch (error) {
        console.error('Erreur lors de la sauvegarde des budgets par catégorie locaux:', error);
        return false;
      }
    },

    getBudgetForCategory: async (category: string): Promise<{ category: string, amount: number, spent: number }> => {
      try {
        const budgetsJson = await AsyncStorage.getItem('budgets');
        const budgets = budgetsJson ? JSON.parse(budgetsJson) : [];
        const budget = budgets.find((b: any) => b.category === category);
        return budget || { category, amount: 0, spent: 0 };
      } catch (error) {
        console.error('Erreur lors de la récupération du budget pour la catégorie locale:', error);
        return { category, amount: 0, spent: 0 };
      }
    },

    calculateTotalBudget: async (): Promise<number> => {
      try {
        const budgetsJson = await AsyncStorage.getItem('budgets');
        const budgets = budgetsJson ? JSON.parse(budgetsJson) : [];
        return budgets.reduce((total: number, budget: any) => total + budget.amount, 0);
      } catch (error) {
        console.error('Erreur lors du calcul du budget total local:', error);
        return 0;
      }
    },
    
    calculateTotalSpent: async (): Promise<number> => {
      try {
        const expensesJson = await AsyncStorage.getItem('expenses');
        const expenses = expensesJson ? JSON.parse(expensesJson) : [];
        return expenses.reduce((total: number, expense: any) => total + expense.amount, 0);
      } catch (error) {
        console.error('Erreur lors du calcul des dépenses totales locales:', error);
        return 0;
      }
    },
    
    updateCategoryBudget: async (category: string, amount: number): Promise<{ success: boolean, error?: any }> => {
      try {
        const budgetsJson = await AsyncStorage.getItem('budgets');
        let budgets = budgetsJson ? JSON.parse(budgetsJson) : [];
        
        const existingIndex = budgets.findIndex((b: any) => b.category === category);
        if (existingIndex >= 0) {
          budgets[existingIndex].amount = amount;
        } else {
          budgets.push({ category, amount, spent: 0 });
        }
        
        await AsyncStorage.setItem('budgets', JSON.stringify(budgets));
        return { success: true };
      } catch (error) {
        console.error('Erreur lors de la mise à jour du budget local:', error);
        return { success: false, error };
      }
    },
    
    transferBudgetBetweenCategories: async (fromCategory: string, toCategory: string, amount: number): Promise<{ success: boolean, error?: any }> => {
      try {
        const budgetsJson = await AsyncStorage.getItem('budgets');
        let budgets = budgetsJson ? JSON.parse(budgetsJson) : [];
        
        const fromIndex = budgets.findIndex((b: any) => b.category === fromCategory);
        if (fromIndex < 0) {
          return { success: false, error: new Error(`La catégorie source ${fromCategory} n'existe pas`) };
        }
        
        if (budgets[fromIndex].amount < amount) {
          return { success: false, error: new Error("Budget insuffisant pour le transfert") };
        }
        
        budgets[fromIndex].amount -= amount;
        
        const toIndex = budgets.findIndex((b: any) => b.category === toCategory);
        if (toIndex >= 0) {
          budgets[toIndex].amount += amount;
        } else {
          budgets.push({ category: toCategory, amount, spent: 0 });
        }
        
        await AsyncStorage.setItem('budgets', JSON.stringify(budgets));
        return { success: true };
      } catch (error) {
        console.error('Erreur lors du transfert de budget local:', error);
        return { success: false, error };
      }
    },
    
    resetAllBudgets: async (): Promise<{ success: boolean, error?: any }> => {
      try {
        const budgetsJson = await AsyncStorage.getItem('budgets');
        let budgets = budgetsJson ? JSON.parse(budgetsJson) : [];
        
        budgets = budgets.map((b: any) => ({ ...b, amount: 0, spent: 0 }));
        
        await AsyncStorage.setItem('budgets', JSON.stringify(budgets));
        return { success: true };
      } catch (error) {
        console.error('Erreur lors de la réinitialisation des budgets locaux:', error);
        return { success: false, error };
      }
    },
    
    // Expense services
    getUserExpenses: async (): Promise<any[]> => {
      try {
        const expensesJson = await AsyncStorage.getItem('expenses');
        return expensesJson ? JSON.parse(expensesJson) : [];
      } catch (error) {
        console.error('Erreur lors de la récupération des dépenses locales:', error);
        return [];
      }
    },
    
    addExpense: async (amount: number, category: string, date: string): Promise<{ success: boolean, expense?: any, error?: any }> => {
      try {
        const expensesJson = await AsyncStorage.getItem('expenses');
        let expenses = expensesJson ? JSON.parse(expensesJson) : [];
        
        const expenseId = Date.now().toString();
        const now = new Date().toISOString();
        
        const newExpense = {
          id: expenseId,
          amount,
          category,
          date,
          createdAt: now,
          userId: 'local-user'
        };
        
        expenses.push(newExpense);
        await AsyncStorage.setItem('expenses', JSON.stringify(expenses));
        
        // Mettre à jour le montant dépensé dans la catégorie
        const budgetsJson = await AsyncStorage.getItem('budgets');
        let budgets = budgetsJson ? JSON.parse(budgetsJson) : [];
        
        const budgetIndex = budgets.findIndex((b: any) => b.category === category);
        if (budgetIndex >= 0) {
          budgets[budgetIndex].spent = (budgets[budgetIndex].spent || 0) + amount;
          await AsyncStorage.setItem('budgets', JSON.stringify(budgets));
        }
        
        return { success: true, expense: newExpense };
      } catch (error) {
        console.error('Erreur lors de l\'ajout de la dépense locale:', error);
        return { success: false, error };
      }
    },
    
    deleteExpense: async (expenseId: string): Promise<{ success: boolean, error?: any }> => {
      try {
        const expensesJson = await AsyncStorage.getItem('expenses');
        let expenses = expensesJson ? JSON.parse(expensesJson) : [];
        
        const expenseIndex = expenses.findIndex((e: any) => e.id === expenseId);
        if (expenseIndex < 0) {
          return { success: false, error: new Error("Dépense non trouvée") };
        }
        
        const expense = expenses[expenseIndex];
        expenses.splice(expenseIndex, 1);
        
        await AsyncStorage.setItem('expenses', JSON.stringify(expenses));
        
        // Mettre à jour le montant dépensé dans la catégorie
        const budgetsJson = await AsyncStorage.getItem('budgets');
        let budgets = budgetsJson ? JSON.parse(budgetsJson) : [];
        
        const budgetIndex = budgets.findIndex((b: any) => b.category === expense.category);
        if (budgetIndex >= 0) {
          budgets[budgetIndex].spent = Math.max(0, (budgets[budgetIndex].spent || 0) - expense.amount);
          await AsyncStorage.setItem('budgets', JSON.stringify(budgets));
        }
        
        return { success: true };
      } catch (error) {
        console.error('Erreur lors de la suppression de la dépense locale:', error);
        return { success: false, error };
      }
    },
    
    resetAllExpenses: async (): Promise<{ success: boolean, error?: any }> => {
      try {
        await AsyncStorage.setItem('expenses', JSON.stringify([]));
        
        // Réinitialiser les montants dépensés dans les budgets
        const budgetsJson = await AsyncStorage.getItem('budgets');
        let budgets = budgetsJson ? JSON.parse(budgetsJson) : [];
        
        budgets = budgets.map((b: any) => ({ ...b, spent: 0 }));
        
        await AsyncStorage.setItem('budgets', JSON.stringify(budgets));
        return { success: true };
      } catch (error) {
        console.error('Erreur lors de la réinitialisation des dépenses locales:', error);
        return { success: false, error };
      }
    },

    getExpensesByDate: async (date: string): Promise<any[]> => {
      try {
        const expensesJson = await AsyncStorage.getItem('expenses');
        const expenses = expensesJson ? JSON.parse(expensesJson) : [];
        return expenses.filter((e: any) => e.date === date);
      } catch (error) {
        console.error('Erreur lors de la récupération des dépenses par date locales:', error);
        return [];
      }
    },

    filterExpenses: async (filters: any): Promise<any[]> => {
      try {
        const expensesJson = await AsyncStorage.getItem('expenses');
        let expenses = expensesJson ? JSON.parse(expensesJson) : [];

        if (filters.category) {
          expenses = expenses.filter((e: any) => e.category === filters.category);
        }

        if (filters.startDate) {
          expenses = expenses.filter((e: any) => e.date >= filters.startDate);
        }

        if (filters.endDate) {
          expenses = expenses.filter((e: any) => e.date <= filters.endDate);
        }

        return expenses;
      } catch (error) {
        console.error('Erreur lors du filtrage des dépenses locales:', error);
        return [];
      }
    }
  };
}

// Exporter les services de secours
export const fallbackServices = createFallbackServices();

// Fonction pour obtenir l'utilisateur actuel
export const getCurrentUser = async () => {
  try {
    if (auth && auth.currentUser) {
      return auth.currentUser;
    }

    // Fallback vers AsyncStorage
    const userJson = await AsyncStorage.getItem('user_data');
    if (userJson) {
      return JSON.parse(userJson);
    }

    return null;
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'utilisateur:', error);
    return null;
  }
};

// Export the services
export {
    app,
    auth,
    db, loginUser, registerUser, resetPassword, signInWithApple, signInWithFacebook, signInWithGoogle
};

