import React, { createContext, useContext, useState } from 'react';

const ExpenseContext = createContext<any>(null);

export function ExpenseProvider({ children }: { children: React.ReactNode }) {
  const [expenses, setExpenses] = useState<any[]>([]);
  return <ExpenseContext.Provider value={{ expenses, setExpenses }}>{children}</ExpenseContext.Provider>;
}

export const useExpense = () => useContext(ExpenseContext);