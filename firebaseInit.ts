import { getApp, getApps, initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

// Configuration Firebase
const firebaseConfig = {
  apiKey: "AIzaSyDZYVxe-GZ1xWLl2UYVu_hmHCjZM9lNxTs",
  authDomain: "mboabudget.firebaseapp.com",
  projectId: "mboabudget",
  storageBucket: "mboabudget.appspot.com",
  messagingSenderId: "1045282978405",
  appId: "1:1045282978405:web:c9e9f1a7a4d8f3a5a5a5a5"
};

// Initialiser Firebase
export const initializeFirebase = () => {
  try {
    let app;
    let auth;
    let db;

    if (getApps().length === 0) {
      console.log("Initializing new Firebase app");
      app = initializeApp(firebaseConfig);
    } else {
      console.log("Using existing Firebase app");
      app = getApp();
    }

    try {
      auth = getAuth(app);
      console.log("Initializing new auth instance with AsyncStorage");
    } catch (error) {
      console.log("Auth initialization error:", error);
      auth = null;
    }

    db = getFirestore(app);

    return { app, auth, db };
  } catch (error) {
    console.error("Firebase initialization error:", error);
    return { app: null, auth: null, db: null };
  }
};

// Créer un objet pour les services de fallback
export const fallbackServices = {
  getCategoryBudgets: async () => [],
  getGlobalBudget: async () => 0,
  calculateTotalSpent: async () => 0,
  updateCategoryBudget: async () => ({ success: false }),
  transferBudgetBetweenCategories: async () => ({ success: false }),
  calculateTotalBudget: async () => 0,
  resetAllBudgets: async () => false,
  saveGlobalBudget: async () => false,
  saveCategoryBudgets: async () => false,
  getBudgetForCategory: async (category) => ({ category, amount: 0, spent: 0 }),
  addExpense: async () => ({ success: false }),
  getUserExpenses: async () => [],
  deleteExpense: async () => ({ success: false }),
  getExpensesByDate: async () => []
};