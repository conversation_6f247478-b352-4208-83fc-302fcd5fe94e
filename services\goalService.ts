import AsyncStorage from '@react-native-async-storage/async-storage';

// Type pour les objectifs financiers
export interface FinancialGoal {
  id: string;
  name: string;
  target: number;
  current: number;
  createdAt: number;
}

// Récupérer tous les objectifs financiers
export const getFinancialGoals = async (): Promise<FinancialGoal[]> => {
  try {
    const goalsJson = await AsyncStorage.getItem('financialGoals');
    if (goalsJson) {
      return JSON.parse(goalsJson);
    }
    return [];
  } catch (error) {
    console.error('Erreur lors de la récupération des objectifs financiers:', error);
    return [];
  }
};

// Ajouter un nouvel objectif financier
export const addFinancialGoal = async (goal: Omit<FinancialGoal, 'id' | 'createdAt'>): Promise<FinancialGoal> => {
  try {
    const goals = await getFinancialGoals();
    
    const newGoal: FinancialGoal = {
      ...goal,
      id: Date.now().toString(),
      createdAt: Date.now()
    };
    
    const updatedGoals = [...goals, newGoal];
    await AsyncStorage.setItem('financialGoals', JSON.stringify(updatedGoals));
    
    return newGoal;
  } catch (error) {
    console.error('Erreur lors de l\'ajout d\'un objectif financier:', error);
    throw error;
  }
};

// Mettre à jour un objectif financier
export const updateFinancialGoal = async (goalId: string, updates: Partial<FinancialGoal>): Promise<FinancialGoal | null> => {
  try {
    const goals = await getFinancialGoals();
    const goalIndex = goals.findIndex(goal => goal.id === goalId);
    
    if (goalIndex === -1) {
      return null;
    }
    
    const updatedGoal = { ...goals[goalIndex], ...updates };
    goals[goalIndex] = updatedGoal;
    
    await AsyncStorage.setItem('financialGoals', JSON.stringify(goals));
    
    return updatedGoal;
  } catch (error) {
    console.error('Erreur lors de la mise à jour d\'un objectif financier:', error);
    throw error;
  }
};

// Supprimer un objectif financier
export const deleteFinancialGoal = async (goalId: string): Promise<boolean> => {
  try {
    const goals = await getFinancialGoals();
    const updatedGoals = goals.filter(goal => goal.id !== goalId);
    
    if (updatedGoals.length === goals.length) {
      return false; // Aucun objectif n'a été supprimé
    }
    
    await AsyncStorage.setItem('financialGoals', JSON.stringify(updatedGoals));
    
    return true;
  } catch (error) {
    console.error('Erreur lors de la suppression d\'un objectif financier:', error);
    throw error;
  }
};

// Mettre à jour le montant actuel d'un objectif financier
export const updateGoalProgress = async (goalId: string, amount: number): Promise<FinancialGoal | null> => {
  try {
    const goals = await getFinancialGoals();
    const goalIndex = goals.findIndex(goal => goal.id === goalId);
    
    if (goalIndex === -1) {
      return null;
    }
    
    const goal = goals[goalIndex];
    const updatedGoal = { 
      ...goal, 
      current: Math.min(goal.current + amount, goal.target) 
    };
    
    goals[goalIndex] = updatedGoal;
    
    await AsyncStorage.setItem('financialGoals', JSON.stringify(goals));
    
    return updatedGoal;
  } catch (error) {
    console.error('Erreur lors de la mise à jour du progrès d\'un objectif financier:', error);
    throw error;
  }
};