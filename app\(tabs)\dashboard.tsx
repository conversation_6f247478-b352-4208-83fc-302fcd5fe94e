import Dashboard from '@/components/Dashboard';
import { getCurrentUser } from '@/firebaseConfig';
import {
    calculateTotalBudget,
    calculateTotalSpent
} from '@/services/budgetService';
import {
    FinancialGoal,
    addFinancialGoal,
    getFinancialGoals
} from '@/services/goalService';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function DashboardScreen() {
  const router = useRouter();
  const [userName, setUserName] = useState('Utilisateur');
  const [income, setIncome] = useState(0);
  const [expenditure, setExpenditure] = useState(0);
  const [balance, setBalance] = useState(0);
  const [financialGoals, setFinancialGoals] = useState<FinancialGoal[]>([]);

  useEffect(() => {
    loadUserData();
    loadFinancialGoals();
  }, []);

  const loadUserData = async () => {
    try {
      // Charger les données de l'utilisateur
      const currentUser = await getCurrentUser();
      if (currentUser) {
        setUserName(currentUser.displayName || 'Utilisateur');
      }
      
      // Charger le budget total
      const totalBudget = await calculateTotalBudget();
      setIncome(totalBudget);
      
      // Charger les dépenses totales
      const totalSpent = await calculateTotalSpent();
      setExpenditure(totalSpent);
      
      // Calculer le solde
      setBalance(Math.max(0, totalBudget - totalSpent));
    } catch (error) {
      console.error('Erreur lors du chargement des données utilisateur:', error);
    }
  };

  const loadFinancialGoals = async () => {
    try {
      const goals = await getFinancialGoals();
      setFinancialGoals(goals);
    } catch (error) {
      console.error('Erreur lors du chargement des objectifs financiers:', error);
    }
  };

  const handleAddGoal = async (newGoal: Omit<FinancialGoal, 'id' | 'createdAt'>) => {
    try {
      await addFinancialGoal(newGoal);
      loadFinancialGoals(); // Recharger les objectifs après l'ajout
    } catch (error) {
      console.error('Erreur lors de l\'ajout d\'un objectif financier:', error);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      <Dashboard 
        navigation={router}
        userName={userName}
        income={income}
        expenditure={expenditure}
        balance={balance}
        financialGoals={financialGoals}
        onAddGoal={handleAddGoal}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
});

